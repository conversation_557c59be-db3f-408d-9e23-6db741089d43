# دليل سريع - تخصيص رسائل Telegram

## 🚀 البدء السريع

### 1. الوصول لتخصيص الرسائل
```
لوحة الإدارة → الإعدادات → إشعارات Telegram → تبويب "تخصيص الرسائل"
```

### 2. المتغيرات الأساسية
```
{order_number}     - رقم الطلب
{customer_name}    - اسم العميل  
{customer_phone}   - رقم الهاتف
{order_total}      - المجموع الإجمالي
{order_items}      - قائمة المنتجات
{order_status}     - حالة الطلب
```

### 3. قالب بسيط
```
🛍️ طلب #{order_number}

👤 {customer_name}
📱 {customer_phone}
💰 {order_total}

📦 المنتجات:
{order_items}
```

## 🔧 حل مشكلة السعر الإجمالي

### إذا كان السعر يظهر 0دج:

1. **تحقق من إعدادات العملة**:
   ```
   WooCommerce → الإعدادات → عام → خيارات العملة
   ```

2. **تأكد من وجود سعر للمنتجات**:
   ```
   المنتجات → تحرير المنتج → بيانات المنتج → السعر العادي
   ```

3. **اختبر تنسيق السعر**:
   ```
   الإعدادات → اختبار Telegram (في وضع التطوير)
   ```

### إعدادات العملة المقترحة للجزائر:
```
العملة: DZD (دينار جزائري)
رمز العملة: دج
موضع العملة: يمين مع مسافة
فاصل الآلاف: ,
فاصل العشرية: .
عدد الخانات العشرية: 2
```

## 📝 أمثلة قوالب جاهزة

### قالب مفصل:
```
🛍️ *طلب جديد #{order_number}*

👤 *العميل:* {customer_name}
📱 *الهاتف:* {customer_phone}
📧 *الإيميل:* {customer_email}

📍 *العنوان:*
{shipping_address}
{shipping_state}, {shipping_country}

🚚 *التوصيل:* {shipping_method}
💳 *الدفع:* {payment_method}

💰 *المجموع:* {order_total}

📦 *المنتجات:*
{order_items}

📋 *الحالة:* {order_status}
📅 *التاريخ:* {order_date}

💬 *ملاحظات:* {order_notes}
```

### قالب مختصر:
```
🛒 طلب #{order_number}
👤 {customer_name} | 📱 {customer_phone}
💰 {order_total}
📦 {order_items}
```

### قالب للطوارئ:
```
🚨 طلب عاجل #{order_number}

العميل: {customer_name}
الهاتف: {customer_phone}
المبلغ: {order_total}

المنتجات:
{order_items}

العنوان: {shipping_address}, {shipping_state}
```

## ⚡ نصائح سريعة

### 1. استخدام الرموز التعبيرية:
```
🛍️ للطلبات
👤 للعملاء  
📱 للهاتف
💰 للأسعار
📦 للمنتجات
📍 للعناوين
🚚 للتوصيل
💳 للدفع
```

### 2. تنسيق النص:
```
*نص عريض*
_نص مائل_
`نص بخط ثابت`
```

### 3. فصل الأقسام:
```
استخدم أسطر فارغة لفصل الأقسام
---
أو خطوط للفصل
```

## 🔍 استكشاف الأخطاء

### المشكلة: الرسالة لا تظهر بشكل صحيح
**الحل**: تحقق من استخدام المتغيرات بالشكل الصحيح `{variable_name}`

### المشكلة: السعر يظهر 0
**الحل**: 
1. تحقق من إعدادات العملة في WooCommerce
2. تأكد من وجود أسعار للمنتجات
3. استخدم صفحة الاختبار للتشخيص

### المشكلة: الرسالة طويلة جداً
**الحل**: اختصر القالب أو احذف المتغيرات غير الضرورية

### المشكلة: المعاينة لا تعمل
**الحل**: تحقق من اتصال الإنترنت وإعدادات WordPress

## 📞 الحصول على المساعدة

### 1. استخدم صفحة الاختبار:
```
الإعدادات → اختبار Telegram (في وضع التطوير)
```

### 2. تحقق من سجل الأخطاء:
```
wp-content/debug.log
```

### 3. اختبر البوت:
```
أرسل /start للبوت في Telegram
```

---

**نصيحة**: احفظ نسخة من القالب المخصص قبل إجراء تغييرات كبيرة!
