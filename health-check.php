<?php
/**
 * فحص صحة إضافة Telegram Notifications
 * يتحقق من جميع الإعدادات والمتطلبات
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// دالة فحص صحة الإضافة
function telegram_health_check() {
    $results = [];
    
    // 1. فحص WooCommerce
    if (!class_exists('WooCommerce')) {
        $results['woocommerce'] = [
            'status' => 'error',
            'message' => 'WooCommerce غير مثبت أو غير مفعل'
        ];
    } else {
        $results['woocommerce'] = [
            'status' => 'success',
            'message' => 'WooCommerce مثبت ومفعل - الإصدار: ' . WC()->version
        ];
    }
    
    // 2. فحص إعدادات البوت
    $options = get_option('telegram_settings');
    $bot_token = isset($options['bot_token']) ? $options['bot_token'] : '';
    
    if (empty($bot_token)) {
        $results['bot_token'] = [
            'status' => 'error',
            'message' => 'رمز البوت غير محدد'
        ];
    } else {
        $results['bot_token'] = [
            'status' => 'success',
            'message' => 'رمز البوت محدد'
        ];
    }
    
    // 3. فحص معرفات المستخدمين
    $chat_ids = isset($options['chat_ids']) ? $options['chat_ids'] : '';
    if (empty($chat_ids)) {
        $results['chat_ids'] = [
            'status' => 'warning',
            'message' => 'لم يتم إضافة أي مستخدمين'
        ];
    } else {
        $chat_ids_array = array_map('trim', explode(',', $chat_ids));
        $count = count($chat_ids_array);
        $results['chat_ids'] = [
            'status' => 'success',
            'message' => "تم إضافة {$count} مستخدم"
        ];
    }
    
    // 4. فحص إعدادات العملة
    $currency = get_woocommerce_currency();
    $currency_symbol = get_woocommerce_currency_symbol();
    
    if (empty($currency) || empty($currency_symbol)) {
        $results['currency'] = [
            'status' => 'warning',
            'message' => 'إعدادات العملة غير مكتملة'
        ];
    } else {
        $results['currency'] = [
            'status' => 'success',
            'message' => "العملة: {$currency} ({$currency_symbol})"
        ];
    }
    
    // 5. فحص قالب الرسالة
    $message_template = isset($options['message_template']) ? $options['message_template'] : '';
    if (empty($message_template)) {
        $results['message_template'] = [
            'status' => 'info',
            'message' => 'سيتم استخدام القالب الافتراضي'
        ];
    } else {
        $results['message_template'] = [
            'status' => 'success',
            'message' => 'قالب رسالة مخصص محدد'
        ];
    }
    
    // 6. فحص SSL
    if (!is_ssl()) {
        $results['ssl'] = [
            'status' => 'warning',
            'message' => 'الموقع لا يستخدم SSL - قد يؤثر على Webhook'
        ];
    } else {
        $results['ssl'] = [
            'status' => 'success',
            'message' => 'الموقع يستخدم SSL'
        ];
    }
    
    // 7. فحص اتصال الإنترنت
    $test_url = 'https://api.telegram.org/bot' . $bot_token . '/getMe';
    $response = wp_remote_get($test_url, ['timeout' => 10]);
    
    if (is_wp_error($response)) {
        $results['connection'] = [
            'status' => 'error',
            'message' => 'فشل في الاتصال بـ Telegram API: ' . $response->get_error_message()
        ];
    } else {
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (isset($data['ok']) && $data['ok']) {
            $bot_name = $data['result']['first_name'] ?? 'غير معروف';
            $results['connection'] = [
                'status' => 'success',
                'message' => "الاتصال بـ Telegram API ناجح - البوت: {$bot_name}"
            ];
        } else {
            $results['connection'] = [
                'status' => 'error',
                'message' => 'رمز البوت غير صحيح أو البوت غير نشط'
            ];
        }
    }
    
    // 8. فحص حالات الطلب
    $order_statuses = isset($options['order_statuses']) ? $options['order_statuses'] : [];
    if (empty($order_statuses)) {
        $results['order_statuses'] = [
            'status' => 'warning',
            'message' => 'لم يتم تحديد حالات طلب للإشعارات'
        ];
    } else {
        $count = count($order_statuses);
        $results['order_statuses'] = [
            'status' => 'success',
            'message' => "تم تحديد {$count} حالة طلب للإشعارات"
        ];
    }
    
    return $results;
}

// دالة عرض نتائج الفحص
function display_health_check_results() {
    $results = telegram_health_check();
    
    echo "<div class='wrap'>";
    echo "<h1>🏥 فحص صحة إضافة Telegram Notifications</h1>";
    echo "<div style='background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);'>";
    
    foreach ($results as $check => $result) {
        $icon = '';
        $color = '';
        
        switch ($result['status']) {
            case 'success':
                $icon = '✅';
                $color = '#46b450';
                break;
            case 'warning':
                $icon = '⚠️';
                $color = '#ffb900';
                break;
            case 'error':
                $icon = '❌';
                $color = '#dc3232';
                break;
            case 'info':
                $icon = 'ℹ️';
                $color = '#0073aa';
                break;
        }
        
        echo "<div style='padding: 10px; margin: 10px 0; border-right: 3px solid {$color}; background: #f8f9fa;'>";
        echo "<strong>{$icon} " . ucfirst(str_replace('_', ' ', $check)) . ":</strong> ";
        echo $result['message'];
        echo "</div>";
    }
    
    // إحصائيات إضافية
    echo "<h3>📊 إحصائيات إضافية:</h3>";
    
    // عدد الإشعارات المرسلة
    $notifications_log = get_option('telegram_notifications_log', []);
    $total_notifications = count($notifications_log);
    echo "<p><strong>إجمالي الإشعارات المرسلة:</strong> {$total_notifications}</p>";
    
    // آخر إشعار
    if (!empty($notifications_log)) {
        $last_notification = end($notifications_log);
        $last_date = date_i18n('Y-m-d H:i:s', $last_notification['timestamp']);
        echo "<p><strong>آخر إشعار:</strong> {$last_date}</p>";
    }
    
    // معلومات المستخدمين
    $telegram_users = get_option('telegram_users_info', []);
    $users_count = count($telegram_users);
    echo "<p><strong>عدد المستخدمين المسجلين:</strong> {$users_count}</p>";
    
    // حالة التوزيع الدوري
    $rotate_notifications = isset($options['rotate_notifications']) && $options['rotate_notifications'];
    $rotation_status = $rotate_notifications ? 'مفعل' : 'معطل';
    echo "<p><strong>التوزيع الدوري:</strong> {$rotation_status}</p>";
    
    echo "</div>";
    echo "</div>";
}

// إضافة صفحة فحص الصحة لقائمة الإدارة
add_action('admin_menu', function() {
    add_submenu_page(
        'options-general.php',
        'فحص صحة Telegram',
        'فحص صحة Telegram',
        'manage_options',
        'telegram-health-check',
        'display_health_check_results'
    );
});

// دالة اختبار سريع للرسالة
function quick_message_test() {
    $options = get_option('telegram_settings');
    $bot_token = isset($options['bot_token']) ? $options['bot_token'] : '';
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
    
    if (empty($bot_token) || empty($chat_ids)) {
        return ['success' => false, 'message' => 'إعدادات البوت غير مكتملة'];
    }
    
    $test_message = "🧪 رسالة اختبار من إضافة Telegram Notifications\n\n" .
                   "⏰ الوقت: " . date_i18n('Y-m-d H:i:s') . "\n" .
                   "✅ الإضافة تعمل بشكل صحيح!";
    
    $first_chat_id = $chat_ids[0];
    
    $response = wp_remote_post("https://api.telegram.org/bot{$bot_token}/sendMessage", [
        'body' => json_encode([
            'chat_id' => $first_chat_id,
            'text' => $test_message,
            'parse_mode' => 'Markdown'
        ]),
        'headers' => ['Content-Type' => 'application/json']
    ]);
    
    if (is_wp_error($response)) {
        return ['success' => false, 'message' => 'فشل في إرسال الرسالة: ' . $response->get_error_message()];
    }
    
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    
    if (isset($data['ok']) && $data['ok']) {
        return ['success' => true, 'message' => 'تم إرسال رسالة الاختبار بنجاح'];
    } else {
        $error_message = isset($data['description']) ? $data['description'] : 'خطأ غير معروف';
        return ['success' => false, 'message' => 'فشل في إرسال الرسالة: ' . $error_message];
    }
}

// معالج AJAX لاختبار الرسالة
add_action('wp_ajax_telegram_quick_test', function() {
    check_ajax_referer('telegram_quick_test', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }
    
    $result = quick_message_test();
    
    if ($result['success']) {
        wp_send_json_success($result['message']);
    } else {
        wp_send_json_error($result['message']);
    }
});
