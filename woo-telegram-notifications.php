<?php
/*
Plugin Name: WooCommerce Telegram Notifications
Plugin URI:
Description: إرسال إشعارات طلبات WooCommerce إلى Telegram مع إمكانية تخصيص الرسائل بالكامل
Version: 1.12.1
Author: Your Name
*/

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تضمين ملفات إضافية
require_once plugin_dir_path(__FILE__) . 'health-check.php';

// تضمين ملف الاختبار في وضع التطوير
if (defined('WP_DEBUG') && WP_DEBUG) {
    require_once plugin_dir_path(__FILE__) . 'test-message-customization.php';
}

// إضافة قائمة الإعدادات
add_action('admin_menu', 'telegram_add_settings_menu');
function telegram_add_settings_menu() {
    $hook = add_options_page(
        'إعدادات إشعارات Telegram',
        'إشعارات Telegram',
        'manage_options',
        'telegram-notifications',
        'telegram_settings_page'
    );

    // إضافة ملف CSS للصفحة
    add_action('admin_print_styles-' . $hook, 'telegram_admin_styles');
}

// تحميل ملفات CSS للإدارة
function telegram_admin_styles() {
    // التحقق من وجود المجلد، وإنشاؤه إذا لم يكن موجوداً
    $assets_dir = plugin_dir_path(__FILE__) . 'assets';
    $css_dir = $assets_dir . '/css';

    if (!file_exists($assets_dir)) {
        mkdir($assets_dir, 0755, true);
    }

    if (!file_exists($css_dir)) {
        mkdir($css_dir, 0755, true);
    }

    // تسجيل وتحميل ملف CSS
    wp_register_style('telegram-admin-style', plugin_dir_url(__FILE__) . 'assets/css/admin-style.css', [], '1.0.0');
    wp_enqueue_style('telegram-admin-style');

    // إضافة jQuery UI للتبويبات
    wp_enqueue_script('jquery-ui-tabs');
}

// عرض صفحة الإعدادات
function telegram_settings_page() {
    ?>
    <div class="wrap telegram-settings-wrap">
        <h1>🛠️ إعدادات إشعارات Telegram</h1>

        <div id="telegram-tabs" class="telegram-tabs">
            <div class="telegram-tab active" data-tab="settings">⚙️ الإعدادات الأساسية</div>
            <div class="telegram-tab" data-tab="message">📝 تخصيص الرسائل</div>
            <div class="telegram-tab" data-tab="users">👥 إدارة المستخدمين</div>
            <div class="telegram-tab" data-tab="rotation">🔄 التوزيع الدوري</div>
            <div class="telegram-tab" data-tab="webhook">🔗 إعداد Webhook</div>
        </div>

        <!-- تبويب الإعدادات الأساسية -->
        <div id="tab-settings" class="telegram-tab-content active telegram-admin-section">
            <h3>الإعدادات الأساسية</h3>
            <form method="post" action="options.php" id="telegram-settings-form">
                <?php
                settings_fields('telegramPlugin');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">رمز البوت (Bot Token)</th>
                        <td><?php telegram_bot_token_render(); ?></td>
                    </tr>
                    <tr>
                        <th scope="row">المستخدمون المستهدفون (Chat IDs)</th>
                        <td><?php telegram_chat_ids_render(); ?></td>
                    </tr>
                    <tr>
                        <th scope="row">حالات الطلب للإشعارات</th>
                        <td><?php telegram_order_statuses_render(); ?></td>
                    </tr>
                    <tr>
                        <th scope="row">التوزيع الدوري للإشعارات</th>
                        <td><?php telegram_rotate_notifications_render(); ?></td>
                    </tr>
                </table>

                <div style="margin: 20px 0;">
                    <?php submit_button('💾 حفظ الإعدادات', 'primary telegram-button', 'submit', false); ?>
                    <button type="button" class="telegram-button" id="quick-test-telegram" style="margin-right: 10px;">
                        🧪 اختبار سريع
                    </button>
                    <span id="quick-test-result" style="display: none; margin-right: 10px;"></span>
                </div>
            </form>

            <script>
            jQuery(document).ready(function($) {
                $('#quick-test-telegram').on('click', function() {
                    var $button = $(this);
                    var $result = $('#quick-test-result');
                    var originalText = $button.html();

                    // إظهار حالة التحميل
                    $button.html('<span class="telegram-loading"></span> جاري الاختبار...');
                    $button.prop('disabled', true);

                    $.post(ajaxurl, {
                        action: 'telegram_quick_test',
                        nonce: '<?php echo wp_create_nonce('telegram_quick_test'); ?>'
                    }, function(response) {
                        if (response.success) {
                            $result.html('<span style="color: #46b450;">✓ ' + response.data + '</span>').show();
                        } else {
                            $result.html('<span style="color: #dc3232;">✗ ' + response.data + '</span>').show();
                        }

                        // إخفاء الرسالة بعد 5 ثواني
                        setTimeout(function() {
                            $result.fadeOut();
                        }, 5000);

                        // استعادة حالة الزر
                        $button.html(originalText);
                        $button.prop('disabled', false);
                    });
                });
            });
            </script>
        </div>

        <!-- تبويب تخصيص الرسائل -->
        <div id="tab-message" class="telegram-tab-content telegram-admin-section">
            <h3>تخصيص الرسائل</h3>
            <?php telegram_message_customization_render(); ?>
        </div>

        <!-- تبويب إدارة المستخدمين -->
        <div id="tab-users" class="telegram-tab-content telegram-admin-section">
            <h3>إدارة المستخدمين</h3>

            <div class="telegram-notice">
                <p class="description">
                    <strong>ℹ️ تعليمات:</strong> يمكنك إدارة جميع المستخدمين من هذه الصفحة. أضف معرفات المستخدمين في الإعدادات الأساسية أولاً، ثم قم بتعديل معلوماتهم وإعداداتهم هنا.
                </p>
            </div>

            <div class="user-management-section">
                <?php telegram_unified_user_management_render(); ?>
            </div>
        </div>

        <!-- تبويب التوزيع الدوري -->
        <div id="tab-rotation" class="telegram-tab-content telegram-admin-section">
            <h3>حالة التوزيع الدوري</h3>
            <?php telegram_rotation_status_render(); ?>
        </div>

        <!-- تبويب إعداد Webhook -->
        <div id="tab-webhook" class="telegram-tab-content telegram-admin-section">
            <h3>🔗 إعداد Webhook</h3>
            <div class="telegram-notice">
                <?php
                $webhook_url = site_url('wp-json/telegram-bot/v1/webhook');
                $options = get_option('telegram_settings');
                $bot_token = $options['bot_token'] ?? '';
                $setup_url = "https://api.telegram.org/bot{$bot_token}/setWebhook?url=" . urlencode($webhook_url);
                ?>
                <p><strong>1.</strong> تأكد من أن موقعك يستخدم شهادة SSL (https)</p>
                <p><strong>2.</strong> انسخ الرابط التالي واستخدمه لإعداد Webhook:</p>
                <div style="background: #fff; padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #ddd;">
                    <code style="display: block; direction: ltr;"><?php echo esc_url($webhook_url); ?></code>
                </div>
                <p><strong>3.</strong> قم بزيارة الرابط التالي لتفعيل Webhook:</p>
                <a href="<?php echo esc_url($setup_url); ?>" target="_blank" class="button telegram-button">⚡ تفعيل Webhook</a>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // تفعيل التبويبات الرئيسية
            $('.telegram-tabs:not(#user-management-tabs) > .telegram-tab').on('click', function() {
                var tabId = $(this).data('tab');

                // تبديل التبويب النشط
                $('.telegram-tabs:not(#user-management-tabs) > .telegram-tab').removeClass('active');
                $(this).addClass('active');

                // إظهار المحتوى المناسب
                $('.telegram-settings-wrap > .telegram-tab-content').removeClass('active');
                $('#tab-' + tabId).addClass('active');

                // تخزين التبويب النشط في localStorage
                localStorage.setItem('telegram_active_tab', tabId);
            });

                        // استعادة التبويب النشط من localStorage
            var activeTab = localStorage.getItem('telegram_active_tab');
            if (activeTab) {
                $('.telegram-tabs > .telegram-tab[data-tab="' + activeTab + '"]').click();
            }
        });
        </script>
    </div>
    <?php
}

// تسجيل الإعدادات
add_action('admin_init', 'telegram_settings_init');
function telegram_settings_init() {
    register_setting('telegramPlugin', 'telegram_settings');

    add_settings_section(
        'telegram_section',
        'إعدادات البوت',
        'telegram_section_description',
        'telegramPlugin'
    );

    add_settings_field(
        'bot_token',
        'رمز البوت (Bot Token)',
        'telegram_bot_token_render',
        'telegramPlugin',
        'telegram_section'
    );

    add_settings_field(
        'chat_ids',
        'المستخدمون المستهدفون (Chat IDs)',
        'telegram_chat_ids_render',
        'telegramPlugin',
        'telegram_section'
    );

    add_settings_field(
        'order_statuses',
        'حالات الطلب للإشعارات',
        'telegram_order_statuses_render',
        'telegramPlugin',
        'telegram_section'
    );

    add_settings_field(
        'message_template',
        'قالب الرسالة',
        'telegram_message_template_render',
        'telegramPlugin',
        'telegram_section'
    );
}

// وصف القسم
function telegram_section_description() {
    echo 'أدخل بيانات البوت الخاص بك على Telegram:';
}

// إضافة خيار التوزيع الدوري في الإعدادات
add_action('admin_init', function() {
    add_settings_field(
        'rotate_notifications',
        'التوزيع الدوري للإشعارات',
        'telegram_rotate_notifications_render',
        'telegramPlugin',
        'telegram_section'
    );

    // إضافة قسم لعرض حالة التوزيع الدوري
    add_settings_field(
        'rotation_status',
        'حالة التوزيع الدوري',
        'telegram_rotation_status_render',
        'telegramPlugin',
        'telegram_section'
    );

    // إضافة قسم لإدارة توفر المستخدمين
    add_settings_field(
        'user_availability',
        'توفر المستخدمين',
        'telegram_user_availability_render',
        'telegramPlugin',
        'telegram_section'
    );

    // إضافة قسم لإدارة أولويات المستخدمين
    add_settings_field(
        'user_priorities',
        'أولويات المستخدمين',
        'telegram_user_priorities_render',
        'telegramPlugin',
        'telegram_section'
    );

    // إضافة قسم لإدارة معلومات المستخدمين
    add_settings_field(
        'user_info',
        'معلومات المستخدمين',
        'telegram_user_info_render',
        'telegramPlugin',
        'telegram_section'
    );
});

// عرض خيار التوزيع الدوري
function telegram_rotate_notifications_render() {
    $options = get_option('telegram_settings');
    $rotate = isset($options['rotate_notifications']) ? $options['rotate_notifications'] : false;
    ?>
    <div class="rotate-notifications-section">
        <label>
            <input type='checkbox' name='telegram_settings[rotate_notifications]'
                   value='1' <?php checked($rotate, true); ?>>
            تفعيل التوزيع الدوري للإشعارات
        </label>

        <div style="margin-top: 10px;">
            <p class="description">
                <strong>🔄 كيف يعمل التوزيع الدوري:</strong>
            </p>
            <ul style="list-style-type: disc; margin-left: 20px; margin-top: 5px;">
                <li>عند وصول طلب جديد، سيتم إرسال الإشعار لمستخدم واحد فقط.</li>
                <li>الطلب التالي سيذهب للمستخدم التالي في القائمة.</li>
                <li>عند الوصول لآخر مستخدم، يعود النظام للمستخدم الأول.</li>
                <li>يضمن هذا النظام توزيع العمل بشكل متساوٍ بين جميع المستخدمين.</li>
                <li>تحديثات حالة الطلب ستذهب للمستخدم الذي تلقى الإشعار الأصلي.</li>
            </ul>

            <p class="description" style="margin-top: 10px;">
                <strong>📝 ملاحظة:</strong> عند تعطيل هذا الخيار، سيتم إرسال كل إشعار لجميع المستخدمين.
            </p>
        </div>
    </div>
    <?php
}

// عرض حالة التوزيع الدوري
function telegram_rotation_status_render() {
    $options = get_option('telegram_settings');
    $rotate = isset($options['rotate_notifications']) && $options['rotate_notifications'];

    if (!$rotate) {
        echo '<div class="telegram-notice warning"><p><strong>⚠️ تنبيه:</strong> نظام التوزيع الدوري غير مفعل حالياً. قم بتفعيله من قسم الإعدادات الأساسية.</p></div>';
        return;
    }

    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
    // إزالة المعرفات المكررة
    $chat_ids = array_unique($chat_ids);

    if (empty($chat_ids)) {
        echo '<div class="telegram-notice warning"><p>لم يتم إضافة أي مستخدمين بعد.</p></div>';
        return;
    }

    $last_user_index = get_option('telegram_last_user_index', -1);
    $next_index = ($last_user_index + 1) % count($chat_ids);
    $next_user = isset($chat_ids[$next_index]) ? $chat_ids[$next_index] : '';

    // الحصول على إحصائيات الإشعارات
    $notifications_log = get_option('telegram_notifications_log', []);
    $user_stats = [];
    $telegram_users = get_option('telegram_users_info', []);

    foreach ($chat_ids as $chat_id) {
        $user_stats[$chat_id] = 0;
    }

    foreach ($notifications_log as $log) {
        if (isset($user_stats[$log['user_id']])) {
            $user_stats[$log['user_id']]++;
        }
    }

    // فرز المستخدمين حسب عدد الإشعارات (تنازلياً)
    arsort($user_stats);

    // الحصول على آخر 10 إشعارات
    $recent_notifications = array_slice($notifications_log, -10);

    // الحصول على اسم المستخدم التالي
    $next_user_name = get_telegram_user_name($next_user);

    // حساب إجمالي الإشعارات
    $total_notifications = array_sum($user_stats);

    ?>
    <div class="rotation-status-section">
        <div class="next-user-box">
            <div>
                <strong>المستخدم التالي في الدورة:</strong>
                <code dir="ltr"><?php echo esc_html($next_user); ?></code>
                <?php if (!empty($next_user_name)) : ?>
                    <span class="user-name">(<?php echo esc_html($next_user_name); ?>)</span>
                <?php endif; ?>
            </div>

            <div style="margin-right: auto;">
                <button type="button" class="telegram-button" id="reset-rotation" style="margin-left: 10px;">
                    <span class="dashicons dashicons-update" style="margin-left: 5px;"></span> إعادة ضبط الدورة
                </button>
            </div>
        </div>

        <div class="telegram-tabs" style="margin-top: 20px;">
            <div class="telegram-tab active" data-tab="stats">إحصائيات الإشعارات</div>
            <div class="telegram-tab" data-tab="recent">آخر الإشعارات</div>
        </div>

        <div id="tab-stats" class="telegram-tab-content active">
            <div class="stats-summary" style="margin-bottom: 15px; display: flex; justify-content: space-between;">
                <div class="stats-card">
                    <div class="stats-title">إجمالي الإشعارات</div>
                    <div class="stats-value"><?php echo esc_html($total_notifications); ?></div>
                </div>
                <div class="stats-card">
                    <div class="stats-title">عدد المستخدمين</div>
                    <div class="stats-value"><?php echo esc_html(count($chat_ids)); ?></div>
                </div>
                <div class="stats-card">
                    <div class="stats-title">متوسط الإشعارات لكل مستخدم</div>
                    <div class="stats-value"><?php echo count($chat_ids) > 0 ? round($total_notifications / count($chat_ids), 1) : 0; ?></div>
                </div>
            </div>

            <table class="telegram-table">
                <thead>
                    <tr>
                        <th width="40%">المستخدم</th>
                        <th width="20%">عدد الإشعارات</th>
                        <th width="40%">النسبة المئوية</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    foreach ($user_stats as $user_id => $count) :
                        $user_name = get_telegram_user_name($user_id);
                        $percentage = $total_notifications > 0 ? round(($count / $total_notifications) * 100, 1) : 0;
                    ?>
                    <tr>
                        <td>
                            <code dir="ltr"><?php echo esc_html($user_id); ?></code>
                            <?php if (!empty($user_name)) : ?>
                                <span class="user-name">(<?php echo esc_html($user_name); ?>)</span>
                            <?php else : ?>
                                <span class="unknown-user">غير معروف</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo esc_html($count); ?></td>
                        <td>
                            <div class="progress-bar-container">
                                <div class="progress-bar" style="width: <?php echo esc_attr($percentage); ?>%;"></div>
                                <span class="progress-text"><?php echo esc_html($percentage); ?>%</span>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div id="tab-recent" class="telegram-tab-content">
            <table class="telegram-table">
                <thead>
                    <tr>
                        <th width="15%">رقم الطلب</th>
                        <th width="35%">المستخدم</th>
                        <th width="20%">النوع</th>
                        <th width="30%">التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if (empty($recent_notifications)) {
                        echo '<tr><td colspan="4" style="text-align: center;">لا توجد إشعارات مسجلة بعد.</td></tr>';
                    } else {
                        foreach (array_reverse($recent_notifications) as $notification) :
                            $type_label = '';
                            $type_class = '';
                            switch ($notification['type']) {
                                case 'new_order':
                                    $type_label = 'طلب جديد';
                                    $type_class = 'new-order';
                                    break;
                                case 'status_change':
                                    $type_label = 'تغيير حالة';
                                    $type_class = 'status-change';
                                    break;
                                case 'status_update':
                                    $type_label = 'تحديث حالة';
                                    $type_class = 'status-update';
                                    break;
                                default:
                                    $type_label = $notification['type'];
                            }

                            $user_name = get_telegram_user_name($notification['user_id']);
                    ?>
                    <tr>
                        <td>
                            <a href="<?php echo admin_url('post.php?post=' . $notification['order_id'] . '&action=edit'); ?>" target="_blank">
                                #<?php echo esc_html($notification['order_id']); ?>
                            </a>
                        </td>
                        <td>
                            <code dir="ltr"><?php echo esc_html($notification['user_id']); ?></code>
                            <?php if (!empty($user_name)) : ?>
                                <span class="user-name">(<?php echo esc_html($user_name); ?>)</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="notification-type <?php echo esc_attr($type_class); ?>">
                                <?php echo esc_html($type_label); ?>
                            </span>
                        </td>
                        <td><?php echo date_i18n('Y-m-d H:i:s', $notification['timestamp']); ?></td>
                    </tr>
                    <?php endforeach;
                    }
                    ?>
                </tbody>
            </table>

            <div style="margin-top: 15px; text-align: left;">
                <button type="button" class="telegram-button" id="clear-notifications-log">
                    <span class="dashicons dashicons-trash" style="margin-left: 5px;"></span> مسح سجل الإشعارات
                </button>
            </div>
        </div>

        <style>
        /* تنسيق الإحصائيات */
        .stats-card {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
            width: 30%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stats-title {
            color: #666;
            font-size: 13px;
            margin-bottom: 5px;
        }

        .stats-value {
            color: #0088cc;
            font-size: 24px;
            font-weight: 600;
        }

        /* تنسيق شريط التقدم */
        .progress-bar-container {
            background-color: #f0f0f1;
            border-radius: 4px;
            height: 20px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar {
            background-color: #0088cc;
            height: 100%;
            border-radius: 4px;
        }

        .progress-text {
            position: absolute;
            top: 0;
            right: 10px;
            line-height: 20px;
            color: #333;
            font-size: 12px;
            font-weight: 500;
        }

        /* تنسيق أنواع الإشعارات */
        .notification-type {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }

        .notification-type.new-order {
            background-color: #e6f7ff;
            color: #0088cc;
        }

        .notification-type.status-change {
            background-color: #fff7e6;
            color: #ff9800;
        }

        .notification-type.status-update {
            background-color: #f0f7ff;
            color: #2196f3;
        }

        /* تنسيق مؤشر التحميل */
        .telegram-loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: telegram-spin 1s linear infinite;
            margin-left: 5px;
            vertical-align: middle;
        }

        @keyframes telegram-spin {
            to {
                transform: rotate(360deg);
            }
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // تفعيل التبويبات
            $('.rotation-status-section .telegram-tab').on('click', function() {
                var tabId = $(this).data('tab');

                // تبديل التبويب النشط
                $('.rotation-status-section .telegram-tab').removeClass('active');
                $(this).addClass('active');

                // إظهار المحتوى المناسب
                $('.rotation-status-section .telegram-tab-content').removeClass('active');
                $('.rotation-status-section #tab-' + tabId).addClass('active');
            });

            // إعادة ضبط التوزيع الدوري
            $('#reset-rotation').on('click', function() {
                var $button = $(this);
                var originalText = $button.html();

                if (confirm('هل أنت متأكد من رغبتك في إعادة ضبط التوزيع الدوري؟ سيتم البدء من المستخدم الأول.')) {
                    // إظهار حالة التحميل
                    $button.html('<span class="telegram-loading"></span> جاري الضبط...');
                    $button.prop('disabled', true);

                    $.post(ajaxurl, {
                        action: 'reset_telegram_rotation',
                        nonce: '<?php echo wp_create_nonce('reset_telegram_rotation'); ?>'
                    }, function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء إعادة الضبط.');
                            $button.html(originalText);
                            $button.prop('disabled', false);
                        }
                    });
                }
            });

            // مسح سجل الإشعارات
            $('#clear-notifications-log').on('click', function() {
                var $button = $(this);
                var originalText = $button.html();

                if (confirm('هل أنت متأكد من رغبتك في مسح سجل الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.')) {
                    // إظهار حالة التحميل
                    $button.html('<span class="telegram-loading"></span> جاري المسح...');
                    $button.prop('disabled', true);

                    $.post(ajaxurl, {
                        action: 'clear_telegram_notifications_log',
                        nonce: '<?php echo wp_create_nonce('clear_telegram_notifications_log'); ?>'
                    }, function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء مسح السجل.');
                            $button.html(originalText);
                            $button.prop('disabled', false);
                        }
                    });
                }
            });
        });
        </script>
    </div>
    <?php
}

// عرض إدارة توفر المستخدمين
function telegram_user_availability_render() {
    $options = get_option('telegram_settings');
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
    $user_availability = get_option('telegram_user_availability', []);
    $telegram_users = get_option('telegram_users_info', []);

    // إزالة المعرفات المكررة
    $chat_ids = array_unique($chat_ids);

    if (empty($chat_ids)) {
        echo '<div class="telegram-notice warning"><p>لم يتم إضافة أي مستخدمين بعد.</p></div>';
        return;
    }

    ?>
    <div class="user-availability-section">
        <div class="telegram-notice">
            <p class="description">
                <strong>ℹ️ تعليمات:</strong> يمكنك تحديد المستخدمين المتاحين لاستلام الإشعارات. المستخدمون غير المتاحين سيتم تخطيهم في نظام التوزيع الدوري.
            </p>
        </div>

        <table class="telegram-table" style="margin-top: 15px;">
            <thead>
                <tr>
                    <th width="40%">معرف المستخدم</th>
                    <th width="40%">الاسم</th>
                    <th width="20%">متاح</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // فرز المستخدمين حسب الاسم
                $users_data = [];
                foreach ($chat_ids as $chat_id) {
                    $user_name = get_telegram_user_name($chat_id);
                    $is_available = !isset($user_availability[$chat_id]) || $user_availability[$chat_id];
                    $users_data[] = [
                        'chat_id' => $chat_id,
                        'user_name' => $user_name,
                        'is_available' => $is_available
                    ];
                }

                // ترتيب المستخدمين حسب الاسم (المستخدمون المعروفون أولاً)
                usort($users_data, function($a, $b) {
                    if (empty($a['user_name']) && !empty($b['user_name'])) return 1;
                    if (!empty($a['user_name']) && empty($b['user_name'])) return -1;
                    return strcmp($a['user_name'], $b['user_name']);
                });

                foreach ($users_data as $user) :
                    $chat_id = $user['chat_id'];
                    $user_name = $user['user_name'];
                    $is_available = $user['is_available'];
                ?>
                <tr>
                    <td>
                        <code dir="ltr"><?php echo esc_html($chat_id); ?></code>
                    </td>
                    <td>
                        <?php if (!empty($user_name)) : ?>
                            <span class="user-name">(<?php echo esc_html($user_name); ?>)</span>
                        <?php else : ?>
                            <span class="unknown-user">غير معروف</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <label class="switch">
                            <input type="checkbox" class="telegram-checkbox" name="telegram_user_availability[<?php echo esc_attr($chat_id); ?>]"
                                   value="1" <?php checked($is_available, true); ?>>
                            <span class="slider round"></span>
                        </label>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div style="margin-top: 20px; text-align: left;">
            <button type="button" class="telegram-button" id="save-user-availability">
                <span class="dashicons dashicons-yes" style="margin-left: 5px;"></span> حفظ حالة التوفر
            </button>
            <span id="availability-status" style="display: none; margin-right: 10px;"></span>
        </div>

        <style>
        /* تنسيق زر التبديل */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #0088cc;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #0088cc;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .slider.round {
            border-radius: 24px;
        }

        .slider.round:before {
            border-radius: 50%;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            $('#save-user-availability').on('click', function() {
                var $button = $(this);
                var $status = $('#availability-status');
                var originalText = $button.html();

                // إظهار حالة التحميل
                $button.html('<span class="telegram-loading"></span> جاري الحفظ...');
                $button.prop('disabled', true);

                var availability = {};

                $('input[name^="telegram_user_availability"]').each(function() {
                    var user_id = $(this).attr('name').match(/\[(.*?)\]/)[1];
                    availability[user_id] = $(this).is(':checked') ? 1 : 0;
                });

                $.post(ajaxurl, {
                    action: 'save_telegram_user_availability',
                    nonce: '<?php echo wp_create_nonce('save_telegram_user_availability'); ?>',
                    availability: availability
                }, function(response) {
                    if (response.success) {
                        $status.html('<span style="color: #46b450;">✓ تم الحفظ بنجاح</span>').show();
                        setTimeout(function() {
                            $status.fadeOut();
                        }, 3000);
                    } else {
                        $status.html('<span style="color: #dc3232;">✗ حدث خطأ أثناء الحفظ</span>').show();
                    }

                    // استعادة حالة الزر
                    $button.html(originalText);
                    $button.prop('disabled', false);
                });
            });
        });
        </script>
    </div>
    <?php
}

// عرض إدارة أولويات المستخدمين
function telegram_user_priorities_render() {
    $options = get_option('telegram_settings');
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
    $user_priorities = get_option('telegram_user_priorities', []);
    $telegram_users = get_option('telegram_users_info', []);

    // إزالة المعرفات المكررة
    $chat_ids = array_unique($chat_ids);

    if (empty($chat_ids)) {
        echo '<div class="telegram-notice warning"><p>لم يتم إضافة أي مستخدمين بعد.</p></div>';
        return;
    }

    ?>
    <div class="user-priorities-section">
        <div class="telegram-notice">
            <p class="description">
                <strong>ℹ️ تعليمات:</strong> يمكنك تعيين أولويات للمستخدمين. المستخدمون ذوو الأولوية الأعلى سيتلقون إشعارات أكثر من غيرهم.
                <br>القيمة الافتراضية هي 1. القيم الأعلى تعني أولوية أعلى (من 1 إلى 10).
            </p>
        </div>

        <table class="telegram-table" style="margin-top: 15px;">
            <thead>
                <tr>
                    <th width="35%">معرف المستخدم</th>
                    <th width="35%">الاسم</th>
                    <th width="30%">الأولوية</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // فرز المستخدمين حسب الاسم
                $users_data = [];
                foreach ($chat_ids as $chat_id) {
                    $user_name = get_telegram_user_name($chat_id);
                    $priority = isset($user_priorities[$chat_id]) ? intval($user_priorities[$chat_id]) : 1;
                    $users_data[] = [
                        'chat_id' => $chat_id,
                        'user_name' => $user_name,
                        'priority' => $priority
                    ];
                }

                // ترتيب المستخدمين حسب الاسم (المستخدمون المعروفون أولاً)
                usort($users_data, function($a, $b) {
                    if (empty($a['user_name']) && !empty($b['user_name'])) return 1;
                    if (!empty($a['user_name']) && empty($b['user_name'])) return -1;
                    return strcmp($a['user_name'], $b['user_name']);
                });

                foreach ($users_data as $user) :
                    $chat_id = $user['chat_id'];
                    $user_name = $user['user_name'];
                    $priority = $user['priority'];
                ?>
                <tr>
                    <td>
                        <code dir="ltr"><?php echo esc_html($chat_id); ?></code>
                    </td>
                    <td>
                        <?php if (!empty($user_name)) : ?>
                            <span class="user-name">(<?php echo esc_html($user_name); ?>)</span>
                        <?php else : ?>
                            <span class="unknown-user">غير معروف</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="priority-control">
                            <button type="button" class="priority-btn minus" data-user="<?php echo esc_attr($chat_id); ?>">-</button>
                            <input type="number" class="telegram-input telegram-number-input" name="telegram_user_priorities[<?php echo esc_attr($chat_id); ?>]"
                                   value="<?php echo esc_attr($priority); ?>" min="1" max="10" readonly>
                            <button type="button" class="priority-btn plus" data-user="<?php echo esc_attr($chat_id); ?>">+</button>

                            <div class="priority-indicator">
                                <?php for ($i = 1; $i <= 10; $i++) : ?>
                                    <span class="priority-dot <?php echo ($i <= $priority) ? 'active' : ''; ?>"></span>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div style="margin-top: 20px; text-align: left;">
            <button type="button" class="telegram-button" id="save-user-priorities">
                <span class="dashicons dashicons-yes" style="margin-left: 5px;"></span> حفظ الأولويات
            </button>
            <span id="priorities-status" style="display: none; margin-right: 10px;"></span>
        </div>

        <style>
        /* تنسيق عناصر الأولوية */
        .priority-control {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        .priority-btn {
            width: 25px;
            height: 25px;
            background-color: #f0f0f1;
            border: 1px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .priority-btn:hover {
            background-color: #0088cc;
            color: #fff;
            border-color: #0088cc;
        }

        .priority-btn.minus {
            margin-left: 5px;
        }

        .priority-btn.plus {
            margin-right: 5px;
        }

        .priority-indicator {
            display: flex;
            margin-right: 10px;
            margin-top: 5px;
            width: 100%;
        }

        .priority-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #eee;
            margin-left: 3px;
        }

        .priority-dot.active {
            background-color: #0088cc;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // زيادة ونقصان الأولوية
            $('.priority-btn.plus').on('click', function() {
                var userId = $(this).data('user');
                var $input = $('input[name="telegram_user_priorities[' + userId + ']"]');
                var currentValue = parseInt($input.val());

                if (currentValue < 10) {
                    $input.val(currentValue + 1);
                    updatePriorityIndicator(userId, currentValue + 1);
                }
            });

            $('.priority-btn.minus').on('click', function() {
                var userId = $(this).data('user');
                var $input = $('input[name="telegram_user_priorities[' + userId + ']"]');
                var currentValue = parseInt($input.val());

                if (currentValue > 1) {
                    $input.val(currentValue - 1);
                    updatePriorityIndicator(userId, currentValue - 1);
                }
            });

            // تحديث مؤشر الأولوية
            function updatePriorityIndicator(userId, value) {
                var $dots = $('input[name="telegram_user_priorities[' + userId + ']"]').closest('.priority-control').find('.priority-dot');

                $dots.each(function(index) {
                    if (index < value) {
                        $(this).addClass('active');
                    } else {
                        $(this).removeClass('active');
                    }
                });
            }

            // حفظ الأولويات
            $('#save-user-priorities').on('click', function() {
                var $button = $(this);
                var $status = $('#priorities-status');
                var originalText = $button.html();

                // إظهار حالة التحميل
                $button.html('<span class="telegram-loading"></span> جاري الحفظ...');
                $button.prop('disabled', true);

                var priorities = {};

                $('input[name^="telegram_user_priorities"]').each(function() {
                    var user_id = $(this).attr('name').match(/\[(.*?)\]/)[1];
                    priorities[user_id] = $(this).val();
                });

                $.post(ajaxurl, {
                    action: 'save_telegram_user_priorities',
                    nonce: '<?php echo wp_create_nonce('save_telegram_user_priorities'); ?>',
                    priorities: priorities
                }, function(response) {
                    if (response.success) {
                        $status.html('<span style="color: #46b450;">✓ تم الحفظ بنجاح</span>').show();
                        setTimeout(function() {
                            $status.fadeOut();
                        }, 3000);
                    } else {
                        $status.html('<span style="color: #dc3232;">✗ حدث خطأ أثناء الحفظ</span>').show();
                    }

                    // استعادة حالة الزر
                    $button.html(originalText);
                    $button.prop('disabled', false);
                });
            });
        });
        </script>
    </div>
    <?php
}

// عرض واجهة إدارة المستخدمين الموحدة
function telegram_unified_user_management_render() {
    $options = get_option('telegram_settings');
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
    $telegram_users = get_option('telegram_users_info', []);
    $user_availability = get_option('telegram_user_availability', []);
    $user_priorities = get_option('telegram_user_priorities', []);

    // إزالة المعرفات المكررة
    $chat_ids = array_unique($chat_ids);

    if (empty($chat_ids)) {
        echo '<div class="telegram-notice warning"><p>لم يتم إضافة أي مستخدمين بعد. قم بإضافة معرفات المستخدمين في الإعدادات الأساسية أولاً.</p></div>';
        return;
    }

    // تجهيز بيانات المستخدمين
    $users_data = [];
    foreach ($chat_ids as $chat_id) {
        $username = isset($telegram_users[$chat_id]['username']) ? $telegram_users[$chat_id]['username'] : '';
        $full_name = isset($telegram_users[$chat_id]['full_name']) ? $telegram_users[$chat_id]['full_name'] : '';
        $is_available = !isset($user_availability[$chat_id]) || $user_availability[$chat_id];
        $priority = isset($user_priorities[$chat_id]) ? intval($user_priorities[$chat_id]) : 1;

        $users_data[] = [
            'chat_id' => $chat_id,
            'username' => $username,
            'full_name' => $full_name,
            'is_available' => $is_available,
            'priority' => $priority
        ];
    }

    // ترتيب المستخدمين حسب الاسم الكامل
    usort($users_data, function($a, $b) {
        if (empty($a['full_name']) && !empty($b['full_name'])) return 1;
        if (!empty($a['full_name']) && empty($b['full_name'])) return -1;
        return strcmp($a['full_name'], $b['full_name']);
    });

    ?>
    <div class="unified-user-management">
        <div class="user-management-actions">
            <button type="button" class="telegram-button" id="add-new-user">
                <span class="dashicons dashicons-plus" style="margin-left: 5px;"></span> إضافة مستخدم جديد
            </button>
            <button type="button" class="telegram-button" id="save-all-users">
                <span class="dashicons dashicons-yes" style="margin-left: 5px;"></span> حفظ جميع التغييرات
            </button>
        </div>

        <div id="new-user-form" style="display: none; margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 4px; border-right: 3px solid #0088cc;">
            <h4 style="margin-top: 0;">إضافة مستخدم جديد</h4>
            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <div style="flex: 1;">
                    <label for="new-user-chat-id">معرف المحادثة (Chat ID) <span style="color: red;">*</span></label>
                    <input type="text" id="new-user-chat-id" class="telegram-input" placeholder="أدخل معرف المحادثة" style="width: 100%;">
                </div>
                <div style="flex: 1;">
                    <label for="new-user-full-name">الاسم الكامل</label>
                    <input type="text" id="new-user-full-name" class="telegram-input" placeholder="الاسم الكامل" style="width: 100%;">
                </div>
            </div>
            <div style="margin-top: 10px; text-align: left;">
                <button type="button" class="telegram-button" id="add-user-submit">إضافة المستخدم</button>
                <button type="button" class="button" id="cancel-add-user">إلغاء</button>
            </div>
        </div>

        <table class="telegram-table user-management-table" style="margin-top: 15px;">
            <thead>
                <tr>
                    <th width="20%">معرف المستخدم</th>
                    <th width="25%">الاسم</th>
                    <th width="15%">متاح</th>
                    <th width="15%">الأولوية</th>
                    <th width="25%">الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users_data as $index => $user) :
                    $chat_id = $user['chat_id'];
                    $first_name = $user['first_name'];
                    $last_name = $user['last_name'];
                    $username = $user['username'];
                    $full_name = $user['full_name'];
                    $is_available = $user['is_available'];
                    $priority = $user['priority'];
                ?>
                <tr class="user-row" data-chat-id="<?php echo esc_attr($chat_id); ?>">
                    <td>
                        <code dir="ltr"><?php echo esc_html($chat_id); ?></code>
                    </td>
                    <td>
                        <div class="user-name-display">
                            <?php if (!empty($full_name)) : ?>
                                <span class="user-name">(<?php echo esc_html($full_name); ?>)</span>
                            <?php else : ?>
                                <span class="unknown-user">غير معروف</span>
                            <?php endif; ?>
                            <button type="button" class="edit-user-name button-link">
                                <span class="dashicons dashicons-edit"></span>
                            </button>
                        </div>
                        <div class="user-name-edit" style="display: none;">
                            <input type="text" class="telegram-input user-full-name" value="<?php echo esc_attr($full_name); ?>" placeholder="الاسم الكامل">
                            <div style="margin-top: 5px;">
                                <button type="button" class="save-user-name button-link">
                                    <span class="dashicons dashicons-yes"></span>
                                </button>
                                <button type="button" class="cancel-edit-name button-link">
                                    <span class="dashicons dashicons-no"></span>
                                </button>
                            </div>
                        </div>
                    </td>
                    <td>
                        <label class="switch">
                            <input type="checkbox" class="telegram-checkbox user-availability"
                                   <?php checked($is_available, true); ?>>
                            <span class="slider round"></span>
                        </label>
                    </td>
                    <td>
                        <div class="priority-control">
                            <button type="button" class="priority-btn minus">-</button>
                            <input type="number" class="telegram-input telegram-number-input user-priority"
                                   value="<?php echo esc_attr($priority); ?>" min="1" max="10" readonly>
                            <button type="button" class="priority-btn plus">+</button>
                        </div>
                    </td>
                    <td>
                        <button type="button" class="button remove-user" data-chat-id="<?php echo esc_attr($chat_id); ?>">
                            <span class="dashicons dashicons-trash"></span> حذف
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div id="user-management-status" class="telegram-message" style="display: none; margin-top: 15px;"></div>

        <style>
        /* تنسيق زر التبديل */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #0088cc;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #0088cc;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .slider.round {
            border-radius: 24px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        /* تنسيق عناصر الأولوية */
        .priority-control {
            display: flex;
            align-items: center;
        }

        .priority-btn {
            width: 25px;
            height: 25px;
            background-color: #f0f0f1;
            border: 1px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .priority-btn:hover {
            background-color: #0088cc;
            color: #fff;
            border-color: #0088cc;
        }

        .priority-btn.minus {
            margin-left: 5px;
        }

        .priority-btn.plus {
            margin-right: 5px;
        }

        /* تنسيق أزرار الإجراءات */
        .user-management-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .button-link {
            background: none;
            border: none;
            color: #0073aa;
            text-decoration: underline;
            cursor: pointer;
            padding: 0;
        }

        .button-link:hover {
            color: #00a0d2;
        }

        /* تنسيق الجدول */
        .user-management-table {
            border-collapse: collapse;
            width: 100%;
        }

        .user-management-table th,
        .user-management-table td {
            padding: 12px;
            text-align: right;
            vertical-align: middle;
        }

        .user-management-table tr:hover {
            background-color: #f5f5f5;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {


            // زيادة ونقصان الأولوية
            $('.priority-btn.plus').on('click', function() {
                var $input = $(this).siblings('.user-priority');
                var currentValue = parseInt($input.val());

                if (currentValue < 10) {
                    $input.val(currentValue + 1);
                }
            });

            $('.priority-btn.minus').on('click', function() {
                var $input = $(this).siblings('.user-priority');
                var currentValue = parseInt($input.val());

                if (currentValue > 1) {
                    $input.val(currentValue - 1);
                }
            });

            // تحرير اسم المستخدم
            $('.edit-user-name').on('click', function() {
                var $row = $(this).closest('.user-row');
                $row.find('.user-name-display').hide();
                $row.find('.user-name-edit').show();
            });

            // إلغاء تحرير الاسم
            $('.cancel-edit-name').on('click', function() {
                var $row = $(this).closest('.user-row');
                $row.find('.user-name-edit').hide();
                $row.find('.user-name-display').show();
            });

            // حفظ اسم المستخدم
            $('.save-user-name').on('click', function() {
                var $row = $(this).closest('.user-row');
                var fullName = $row.find('.user-full-name').val().trim();

                if (fullName) {
                    $row.find('.user-name-display .user-name').text('(' + fullName + ')');
                    $row.find('.user-name-display .unknown-user').hide();
                    $row.find('.user-name-display .user-name').show();
                } else {
                    $row.find('.user-name-display .user-name').hide();
                    $row.find('.user-name-display .unknown-user').show();
                }

                $row.find('.user-name-edit').hide();
                $row.find('.user-name-display').show();
            });

            // إظهار نموذج إضافة مستخدم جديد
            $('#add-new-user').on('click', function() {
                $('#new-user-form').slideDown();
            });

            // إلغاء إضافة مستخدم جديد
            $('#cancel-add-user').on('click', function() {
                $('#new-user-form').slideUp();
                $('#new-user-chat-id, #new-user-first-name, #new-user-last-name').val('');
            });

            // إضافة مستخدم جديد
            $('#add-user-submit').on('click', function() {
                var chatId = $('#new-user-chat-id').val().trim();
                var fullName = $('#new-user-full-name').val().trim();

                if (!chatId) {
                    alert('يرجى إدخال معرف المحادثة (Chat ID)');
                    return;
                }

                // التحقق من وجود المستخدم مسبقاً
                var exists = false;
                $('.user-row').each(function() {
                    if ($(this).data('chat-id') == chatId) {
                        exists = true;
                        return false;
                    }
                });

                if (exists) {
                    alert('هذا المستخدم موجود بالفعل!');
                    return;
                }

                // إضافة المستخدم إلى الجدول
                var newRow = `
                <tr class="user-row" data-chat-id="${chatId}">
                    <td>
                        <code dir="ltr">${chatId}</code>
                    </td>
                    <td>
                        <div class="user-name-display">
                            ${fullName ? '<span class="user-name">(' + fullName + ')</span>' : '<span class="unknown-user">غير معروف</span>'}
                            <button type="button" class="edit-user-name button-link">
                                <span class="dashicons dashicons-edit"></span>
                            </button>
                        </div>
                        <div class="user-name-edit" style="display: none;">
                            <input type="text" class="telegram-input user-full-name" value="${fullName}" placeholder="الاسم الكامل">
                            <div style="margin-top: 5px;">
                                <button type="button" class="save-user-name button-link">
                                    <span class="dashicons dashicons-yes"></span>
                                </button>
                                <button type="button" class="cancel-edit-name button-link">
                                    <span class="dashicons dashicons-no"></span>
                                </button>
                            </div>
                        </div>
                    </td>
                    <td>
                        <label class="switch">
                            <input type="checkbox" class="telegram-checkbox user-availability" checked>
                            <span class="slider round"></span>
                        </label>
                    </td>
                    <td>
                        <div class="priority-control">
                            <button type="button" class="priority-btn minus">-</button>
                            <input type="number" class="telegram-input telegram-number-input user-priority"
                                   value="1" min="1" max="10" readonly>
                            <button type="button" class="priority-btn plus">+</button>
                        </div>
                    </td>
                    <td>
                        <button type="button" class="button remove-user" data-chat-id="${chatId}">
                            <span class="dashicons dashicons-trash"></span> حذف
                        </button>
                    </td>
                </tr>
                `;

                $('.user-management-table tbody').append(newRow);

                // إعادة تعيين النموذج
                $('#new-user-form').slideUp();
                $('#new-user-chat-id, #new-user-full-name').val('');

                // إعادة تعيين معالجات الأحداث للصف الجديد
                bindRowEvents();

                // عرض رسالة نجاح
                showStatus('تمت إضافة المستخدم بنجاح. انقر على "حفظ جميع التغييرات" لتأكيد الإضافة.', 'success');
            });

            // حذف مستخدم
            function bindRowEvents() {
                $('.remove-user').off('click').on('click', function() {
                    var chatId = $(this).data('chat-id');

                    if (confirm('هل أنت متأكد من رغبتك في حذف هذا المستخدم؟')) {
                        $(this).closest('tr').remove();
                        showStatus('تم حذف المستخدم. انقر على "حفظ جميع التغييرات" لتأكيد الحذف.', 'success');
                    }
                });

                // إعادة تعيين معالجات الأحداث الأخرى
                $('.edit-user-name').off('click').on('click', function() {
                    var $row = $(this).closest('.user-row');
                    $row.find('.user-name-display').hide();
                    $row.find('.user-name-edit').show();
                });

                $('.cancel-edit-name').off('click').on('click', function() {
                    var $row = $(this).closest('.user-row');
                    $row.find('.user-name-edit').hide();
                    $row.find('.user-name-display').show();
                });

                $('.save-user-name').off('click').on('click', function() {
                    var $row = $(this).closest('.user-row');
                    var fullName = $row.find('.user-full-name').val().trim();

                    if (fullName) {
                        $row.find('.user-name-display .user-name').text('(' + fullName + ')');
                        $row.find('.user-name-display .unknown-user').hide();
                        $row.find('.user-name-display .user-name').show();
                    } else {
                        $row.find('.user-name-display .user-name').hide();
                        $row.find('.user-name-display .unknown-user').show();
                    }

                    $row.find('.user-name-edit').hide();
                    $row.find('.user-name-display').show();
                });

                $('.priority-btn.plus').off('click').on('click', function() {
                    var $input = $(this).siblings('.user-priority');
                    var currentValue = parseInt($input.val());

                    if (currentValue < 10) {
                        $input.val(currentValue + 1);
                    }
                });

                $('.priority-btn.minus').off('click').on('click', function() {
                    var $input = $(this).siblings('.user-priority');
                    var currentValue = parseInt($input.val());

                    if (currentValue > 1) {
                        $input.val(currentValue - 1);
                    }
                });


            }

            // تعيين معالجات الأحداث الأولية
            bindRowEvents();

            // حفظ جميع التغييرات
            $('#save-all-users').on('click', function() {
                var $button = $(this);
                var originalText = $button.html();

                // إظهار حالة التحميل
                $button.html('<span class="telegram-loading"></span> جاري الحفظ...');
                $button.prop('disabled', true);

                // جمع بيانات المستخدمين
                var chatIds = [];
                var userInfo = {};
                var userAvailability = {};
                var userPriorities = {};

                $('.user-row').each(function() {
                    var chatId = $(this).data('chat-id');
                    chatIds.push(chatId);

                    // معلومات المستخدم
                    var fullName = $(this).find('.user-full-name').val().trim();

                    userInfo[chatId] = {
                        'full_name': fullName
                    };

                    // توفر المستخدم
                    userAvailability[chatId] = $(this).find('.user-availability').is(':checked') ? 1 : 0;

                    // أولوية المستخدم
                    userPriorities[chatId] = $(this).find('.user-priority').val();
                });

                console.log('Saving data:', {
                    chatIds: chatIds,
                    userInfo: userInfo,
                    userAvailability: userAvailability,
                    userPriorities: userPriorities
                });

                // حفظ البيانات
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'save_telegram_all_users',
                        nonce: '<?php echo wp_create_nonce('save_telegram_all_users'); ?>',
                        chat_ids: chatIds.join(','),
                        user_info: userInfo,
                        user_availability: userAvailability,
                        user_priorities: userPriorities
                    },
                    success: function(response) {
                        console.log('Response:', response);
                        if (response.success) {
                            showStatus('تم حفظ جميع التغييرات بنجاح.', 'success');
                        } else {
                            showStatus('حدث خطأ أثناء حفظ التغييرات: ' + (response.data || ''), 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        showStatus('حدث خطأ في الاتصال بالخادم: ' + error, 'error');
                    },
                    complete: function() {
                        // استعادة حالة الزر
                        $button.html(originalText);
                        $button.prop('disabled', false);
                    }
                });
            });

            // عرض رسالة الحالة
            function showStatus(message, type) {
                var $status = $('#user-management-status');
                $status.removeClass('success error').addClass(type);
                $status.html(message).fadeIn();

                setTimeout(function() {
                    $status.fadeOut();
                }, 3000);
            }
        });
        </script>
    </div>
    <?php
}

// عرض معلومات المستخدمين
function telegram_user_info_render() {
    $options = get_option('telegram_settings');
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
    $telegram_users = get_option('telegram_users_info', []);

    // إزالة المعرفات المكررة
    $chat_ids = array_unique($chat_ids);

    if (empty($chat_ids)) {
        echo '<div class="telegram-notice warning"><p>لم يتم إضافة أي مستخدمين بعد.</p></div>';
        return;
    }

    ?>
    <div class="user-info-section">
        <div class="telegram-notice">
            <p class="description">
                <strong>ℹ️ تعليمات:</strong> يمكنك تعديل أسماء المستخدمين هنا. هذه الأسماء ستظهر بين قوسين بجانب معرفات المستخدمين في جميع أنحاء الإضافة.
            </p>
        </div>

        <table class="telegram-table" style="margin-top: 15px;">
            <thead>
                <tr>
                    <th width="20%">معرف المستخدم</th>
                    <th width="20%">الاسم الأول</th>
                    <th width="20%">الاسم الأخير</th>
                    <th width="20%">اسم المستخدم</th>
                    <th width="20%">الاسم الكامل</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // فرز المستخدمين حسب الاسم
                $users_data = [];
                foreach ($chat_ids as $chat_id) {
                    $first_name = isset($telegram_users[$chat_id]['first_name']) ? $telegram_users[$chat_id]['first_name'] : '';
                    $last_name = isset($telegram_users[$chat_id]['last_name']) ? $telegram_users[$chat_id]['last_name'] : '';
                    $username = isset($telegram_users[$chat_id]['username']) ? $telegram_users[$chat_id]['username'] : '';
                    $full_name = isset($telegram_users[$chat_id]['full_name']) ? $telegram_users[$chat_id]['full_name'] : '';

                    $users_data[] = [
                        'chat_id' => $chat_id,
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'username' => $username,
                        'full_name' => $full_name
                    ];
                }

                // ترتيب المستخدمين حسب الاسم الكامل
                usort($users_data, function($a, $b) {
                    if (empty($a['full_name']) && !empty($b['full_name'])) return 1;
                    if (!empty($a['full_name']) && empty($b['full_name'])) return -1;
                    return strcmp($a['full_name'], $b['full_name']);
                });

                foreach ($users_data as $user) :
                    $chat_id = $user['chat_id'];
                    $first_name = $user['first_name'];
                    $last_name = $user['last_name'];
                    $username = $user['username'];
                    $full_name = $user['full_name'];
                ?>
                <tr>
                    <td>
                        <code dir="ltr"><?php echo esc_html($chat_id); ?></code>
                    </td>
                    <td>
                        <input type="text" class="telegram-input" name="telegram_user_info[<?php echo esc_attr($chat_id); ?>][first_name]"
                               value="<?php echo esc_attr($first_name); ?>" placeholder="الاسم الأول">
                    </td>
                    <td>
                        <input type="text" class="telegram-input" name="telegram_user_info[<?php echo esc_attr($chat_id); ?>][last_name]"
                               value="<?php echo esc_attr($last_name); ?>" placeholder="الاسم الأخير">
                    </td>
                    <td>
                        <input type="text" class="telegram-input" name="telegram_user_info[<?php echo esc_attr($chat_id); ?>][username]"
                               value="<?php echo esc_attr($username); ?>" placeholder="اسم المستخدم">
                    </td>
                    <td>
                        <input type="text" class="telegram-input" name="telegram_user_info[<?php echo esc_attr($chat_id); ?>][full_name]"
                               value="<?php echo esc_attr($full_name); ?>" placeholder="الاسم الكامل">
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="telegram-notice" style="margin-top: 15px;">
            <p><strong>ملاحظة:</strong> يمكنك ترك الاسم الكامل فارغاً وسيتم إنشاؤه تلقائياً من الاسم الأول والأخير.</p>
        </div>

        <div style="margin-top: 20px; text-align: left;">
            <button type="button" class="telegram-button" id="save-user-info">
                <span class="dashicons dashicons-yes" style="margin-left: 5px;"></span> حفظ معلومات المستخدمين
            </button>
            <span id="user-info-status" style="display: none; margin-right: 10px;"></span>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // تحديث الاسم الكامل تلقائياً عند تغيير الاسم الأول أو الأخير
            $('input[name$="[first_name]"], input[name$="[last_name]"]').on('input', function() {
                var userId = $(this).attr('name').match(/\[(.*?)\]/)[1];
                var $firstName = $('input[name="telegram_user_info[' + userId + '][first_name]"]');
                var $lastName = $('input[name="telegram_user_info[' + userId + '][last_name]"]');
                var $fullName = $('input[name="telegram_user_info[' + userId + '][full_name]"]');

                var firstName = $firstName.val().trim();
                var lastName = $lastName.val().trim();

                if (firstName || lastName) {
                    $fullName.val((firstName + ' ' + lastName).trim());
                }
            });

            // حفظ معلومات المستخدمين
            $('#save-user-info').on('click', function() {
                var $button = $(this);
                var $status = $('#user-info-status');
                var originalText = $button.html();

                // إظهار حالة التحميل
                $button.html('<span class="telegram-loading"></span> جاري الحفظ...');
                $button.prop('disabled', true);

                var user_info = {};

                $('input[name^="telegram_user_info"]').each(function() {
                    var matches = $(this).attr('name').match(/\[(.*?)\]\[(.*?)\]/);
                    if (matches) {
                        var user_id = matches[1];
                        var field = matches[2];

                        if (!user_info[user_id]) {
                            user_info[user_id] = {};
                        }

                        user_info[user_id][field] = $(this).val();
                    }
                });

                $.post(ajaxurl, {
                    action: 'save_telegram_user_info',
                    nonce: '<?php echo wp_create_nonce('save_telegram_user_info'); ?>',
                    user_info: user_info
                }, function(response) {
                    if (response.success) {
                        $status.html('<span style="color: #46b450;">✓ تم الحفظ بنجاح</span>').show();

                        // تحديث الصفحة بعد 1.5 ثانية
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        $status.html('<span style="color: #dc3232;">✗ حدث خطأ أثناء الحفظ</span>').show();

                        // استعادة حالة الزر
                        $button.html(originalText);
                        $button.prop('disabled', false);
                    }
                });
            });
        });
        </script>
    </div>
    <?php
}

// عرض واجهة تخصيص الرسائل
function telegram_message_customization_render() {
    $options = get_option('telegram_settings');
    $message_template = isset($options['message_template']) ? $options['message_template'] : get_default_message_template();
    ?>
    <div class="message-customization-section">
        <form method="post" action="options.php" id="telegram-message-form">
            <?php settings_fields('telegramPlugin'); ?>

            <div class="telegram-notice">
                <p class="description">
                    <strong>ℹ️ تعليمات:</strong> يمكنك تخصيص رسالة الإشعار باستخدام المتغيرات التالية:
                </p>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 13px;">
                        <div><code>{order_number}</code> - رقم الطلب</div>
                        <div><code>{customer_name}</code> - اسم العميل</div>
                        <div><code>{customer_phone}</code> - رقم الهاتف</div>
                        <div><code>{customer_email}</code> - البريد الإلكتروني</div>
                        <div><code>{shipping_country}</code> - الدولة</div>
                        <div><code>{shipping_state}</code> - الولاية</div>
                        <div><code>{shipping_city}</code> - البلدية</div>
                        <div><code>{shipping_address}</code> - العنوان</div>
                        <div><code>{payment_method}</code> - طريقة الدفع</div>
                        <div><code>{shipping_method}</code> - طريقة التوصيل</div>
                        <div><code>{order_total}</code> - المجموع الإجمالي</div>
                        <div><code>{order_items}</code> - قائمة المنتجات</div>
                        <div><code>{order_status}</code> - حالة الطلب</div>
                        <div><code>{order_date}</code> - تاريخ الطلب</div>
                        <div><code>{order_notes}</code> - ملاحظات الطلب</div>
                    </div>
                </div>
            </div>

            <table class="form-table">
                <tr>
                    <th scope="row">قالب الرسالة</th>
                    <td>
                        <textarea name="telegram_settings[message_template]" rows="15" style="width: 100%; max-width: 600px; font-family: monospace; direction: rtl;" placeholder="أدخل قالب الرسالة هنا..."><?php echo esc_textarea($message_template); ?></textarea>
                        <p class="description">استخدم المتغيرات المذكورة أعلاه لتخصيص الرسالة حسب احتياجاتك.</p>
                    </td>
                </tr>
            </table>

            <div style="margin: 20px 0;">
                <button type="button" class="telegram-button" id="save-message-template">
                    💾 حفظ قالب الرسالة
                </button>
                <button type="button" class="telegram-button" id="reset-message-template" style="margin-right: 10px;">
                    🔄 استعادة القالب الافتراضي
                </button>
                <button type="button" class="telegram-button" id="preview-message" style="margin-right: 10px;">
                    👁️ معاينة الرسالة
                </button>
                <span id="template-save-status" style="display: none; margin-right: 10px;"></span>
            </div>
        </form>

        <!-- منطقة المعاينة -->
        <div id="message-preview" style="display: none; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; border-right: 3px solid #0088cc;">
            <h4>معاينة الرسالة:</h4>
            <div id="preview-content" style="background: #fff; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; direction: rtl;"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // حفظ قالب الرسالة
            $('#save-message-template').on('click', function() {
                var $button = $(this);
                var $status = $('#template-save-status');
                var originalText = $button.html();
                var template = $('textarea[name="telegram_settings[message_template]"]').val();

                // إظهار حالة التحميل
                $button.html('<span class="telegram-loading"></span> جاري الحفظ...');
                $button.prop('disabled', true);

                $.post(ajaxurl, {
                    action: 'save_telegram_message_template',
                    nonce: '<?php echo wp_create_nonce('save_telegram_message_template'); ?>',
                    template: template
                }, function(response) {
                    if (response.success) {
                        $status.html('<span style="color: #46b450;">✓ ' + response.data + '</span>').show();
                        setTimeout(function() {
                            $status.fadeOut();
                        }, 3000);
                    } else {
                        $status.html('<span style="color: #dc3232;">✗ ' + response.data + '</span>').show();
                    }

                    // استعادة حالة الزر
                    $button.html(originalText);
                    $button.prop('disabled', false);
                });
            });

            // استعادة القالب الافتراضي
            $('#reset-message-template').on('click', function() {
                if (confirm('هل أنت متأكد من رغبتك في استعادة القالب الافتراضي؟ سيتم فقدان التخصيصات الحالية.')) {
                    $.post(ajaxurl, {
                        action: 'get_default_telegram_template',
                        nonce: '<?php echo wp_create_nonce('get_default_telegram_template'); ?>'
                    }, function(response) {
                        if (response.success) {
                            $('textarea[name="telegram_settings[message_template]"]').val(response.data);
                        }
                    });
                }
            });

            // معاينة الرسالة
            $('#preview-message').on('click', function() {
                var template = $('textarea[name="telegram_settings[message_template]"]').val();

                $.post(ajaxurl, {
                    action: 'preview_telegram_message',
                    nonce: '<?php echo wp_create_nonce('preview_telegram_message'); ?>',
                    template: template
                }, function(response) {
                    if (response.success) {
                        $('#preview-content').text(response.data);
                        $('#message-preview').show();
                    } else {
                        alert('حدث خطأ أثناء المعاينة: ' + response.data);
                    }
                });
            });
        });
        </script>
    </div>
    <?php
}

// الحصول على القالب الافتراضي للرسالة
function get_default_message_template() {
    return "🛍️ *طلب #{order_number}*\n\n" .
           "👤 *الاسم:* {customer_name}\n" .
           "📱 *رقم الهاتف:* {customer_phone}\n" .
           "📧 *البريد الإلكتروني:* {customer_email}\n" .
           "🌍 *الدولة:* {shipping_country}\n" .
           "🏛️ *الولاية:* {shipping_state}\n" .
           "🏘️ *البلدية:* {shipping_city}\n" .
           "📍 *العنوان:* {shipping_address}\n" .
           "🚚 *نوع التوصيل:* {shipping_method}\n" .
           "💳 *طريقة الدفع:* {payment_method}\n\n" .
           "💰 *المجموع:* {order_total}\n\n" .
           "📦 *المنتجات:*\n{order_items}\n" .
           "📋 *الحالة:* {order_status}\n" .
           "📅 *تاريخ الطلب:* {order_date}";
}

// حقل إدخال رمز البوت
function telegram_bot_token_render() {
    $options = get_option('telegram_settings');
    ?>
    <input type='text' style='width: 400px;' name='telegram_settings[bot_token]'
           value='<?php echo isset($options['bot_token']) ? esc_attr($options['bot_token']) : ''; ?>'>
    <p class="description">أدخل رمز البوت الذي حصلت عليه من BotFather.</p>
    <?php
}

// حقل إدخال معرفات المحادثة
function telegram_chat_ids_render() {
    $options = get_option('telegram_settings');
    $chat_ids = isset($options['chat_ids']) ? esc_textarea($options['chat_ids']) : '';
    ?>
    <div style="margin-bottom: 20px;">
        <textarea name='telegram_settings[chat_ids]' rows='3' style='width: 400px;' placeholder="مثال: 123456789, 987654321"><?php echo $chat_ids; ?></textarea>
        <p class="description">أدخل معرفات المستخدمين (Chat IDs) مع الفصل بينها بفاصلة (,).</p>
    </div>

    <div class="notice notice-info" style="margin-top: 15px;">
        <p>
            <strong>📝 كيفية إضافة المستخدمين:</strong>
        </p>
        <ol style="margin-left: 20px; list-style-type: decimal;">
            <li>قم بإضافة البوت إلى تيليجرام من خلال الرابط الخاص به.</li>
            <li>عندما يقوم المستخدم بالضغط على "Start" أو "/start" في البوت، سيظهر له معرف المستخدم الخاص به تلقائياً.</li>
            <li>يمكن للمستخدم نسخ هذا المعرف وإرساله لك.</li>
            <li>قم بإضافة معرف كل مستخدم هنا مع الفصل بين المعرفات بفاصلة (,).</li>
            <li>مثال على الشكل الصحيح: 123456789, 987654321, 456789123</li>
        </ol>
    </div>

    <div class="notice notice-warning" style="margin-top: 15px;">
        <p>
            <strong>⚠️ ملاحظات هامة:</strong>
        </p>
        <ul style="margin-left: 20px; list-style-type: disc;">
            <li>تأكد من استخدام الفاصلة (,) للفصل بين المعرفات.</li>
            <li>لا تستخدم أي رموز أخرى مثل النقطة (.) أو الفاصلة المنقوطة (;).</li>
            <li>تأكد من عدم وجود مسافات في بداية أو نهاية المعرف.</li>
        </ul>
    </div>
    <?php
}

// حقل اختيار حالات الطلب
function telegram_order_statuses_render() {
    $options = get_option('telegram_settings');
    $selected_statuses = isset($options['order_statuses']) ? $options['order_statuses'] : ['processing'];

    $order_statuses = wc_get_order_statuses();
    ?>
    <div style="max-height: 200px; overflow-y: auto; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
        <?php foreach ($order_statuses as $status => $label) :
            $status = str_replace('wc-', '', $status);
            $checked = in_array($status, $selected_statuses) ? 'checked' : '';
        ?>
            <label style="display: block; margin-bottom: 5px;">
                <input type="checkbox" name="telegram_settings[order_statuses][]"
                       value="<?php echo esc_attr($status); ?>" <?php echo $checked; ?>>
                <?php echo esc_html($label); ?>
            </label>
        <?php endforeach; ?>
    </div>
    <p class="description">اختر حالات الطلب التي تريد تلقي إشعارات لها.</p>
    <?php
}

// حقل قالب الرسالة (مخفي - يتم عرضه في التبويب المخصص)
function telegram_message_template_render() {
    // هذا الحقل مخفي لأنه يتم عرضه في تبويب منفصل
    $options = get_option('telegram_settings');
    $message_template = isset($options['message_template']) ? $options['message_template'] : get_default_message_template();
    ?>
    <input type="hidden" name="telegram_settings[message_template]" value="<?php echo esc_attr($message_template); ?>">
    <?php
}

// إعداد Webhook لتيليجرام
add_action('rest_api_init', function() {
    register_rest_route('telegram-bot/v1', '/webhook', array(
        'methods' => 'POST',
        'callback' => 'handle_telegram_webhook',
        'permission_callback' => '__return_true'
    ));
});

// معالجة الرسائل الواردة من تيليجرام
function handle_telegram_webhook($request) {
    $body = $request->get_body();
    $update = json_decode($body, true);
    error_log('Telegram Webhook: Received update: ' . print_r($update, true));

    // التحقق من رسالة start
    if (isset($update['message']) && isset($update['message']['text']) && $update['message']['text'] === '/start') {
        $chat_id = $update['message']['chat']['id'];
        $first_name = $update['message']['chat']['first_name'] ?? '';
        $last_name = $update['message']['chat']['last_name'] ?? '';
        $username = $update['message']['chat']['username'] ?? '';

        // تخزين معلومات المستخدم
        $user_info = [
            'first_name' => $first_name,
            'last_name' => $last_name,
            'username' => $username,
            'full_name' => trim($first_name . ' ' . $last_name)
        ];

        // حفظ معلومات المستخدم
        $telegram_users = get_option('telegram_users_info', []);
        $telegram_users[$chat_id] = $user_info;
        update_option('telegram_users_info', $telegram_users);

        $welcome_message = sprintf(
            "👋 *مرحباً %s!*\n\n" .
            "🆔 *معرف المحادثة الخاص بك هو:*\n" .
            "`%s`\n\n" .
            "📋 *خطوات الاستخدام:*\n" .
            "1️⃣ قم بنسخ المعرف أعلاه (اضغط عليه للنسخ)\n" .
            "2️⃣ أرسله لمدير المتجر\n" .
            "3️⃣ سيقوم المدير بإضافتك لقائمة المستخدمين\n\n" .
            "✅ *عند إضافتك بنجاح:*\n" .
            "• ستتلقى إشعارات الطلبات الجديدة\n" .
            "• يمكنك تغيير حالة الطلبات\n" .
            "• يمكنك متابعة تحديثات الطلبات\n\n" .
            "🔄 *نظام التوزيع الدوري:*\n" .
            "إذا كان مفعلاً، سيتم توزيع إشعارات الطلبات بالتناوب بين جميع المستخدمين لضمان توزيع العمل بشكل عادل.",
            $first_name,
            $chat_id
        );

        error_log('Telegram Bot: Sending welcome message to chat_id: ' . $chat_id);

        // إرسال رسالة الترحيب مباشرة
        $options = get_option('telegram_settings');
        $bot_token = $options['bot_token'] ?? '';

        wp_remote_post("https://api.telegram.org/bot{$bot_token}/sendMessage", [
            'body' => json_encode([
                'chat_id' => $chat_id,
                'text' => $welcome_message,
                'parse_mode' => 'Markdown'
            ]),
            'headers' => ['Content-Type' => 'application/json']
        ]);

        error_log('Telegram Bot: Welcome message sent');
        return new WP_REST_Response(['status' => 'success'], 200);
    }

    // التحقق من وجود callback query (ضغط على زر)
    if (isset($update['callback_query'])) {
        $callback_query = $update['callback_query'];
        $data = $callback_query['data'];
        $chat_id = $callback_query['from']['id'];

        if (preg_match('/order_status_(\d+)_(\w+)/', $data, $matches)) {
            $order_id = $matches[1];
            $new_status = $matches[2];
            $order = wc_get_order($order_id);

            if ($order) {
                // التحقق من أن المستخدم مصرح له بتغيير الحالة
                $options = get_option('telegram_settings');
                $allowed_chat_ids = isset($options['chat_ids']) ?
                    array_map('trim', explode(',', $options['chat_ids'])) : [];

                if (in_array($chat_id, $allowed_chat_ids)) {
                    $order->update_status($new_status);
                    $status_name = $new_status === 'pending' ? 'قيد الإنتظار' : wc_get_order_status_name($new_status);

                    // إرسال رد على الضغطة
                    answer_callback_query($callback_query['id'], "✅ تم تحديث حالة الطلب #{$order_id} إلى: {$status_name}");

                    // تحديث الرسالة الأصلية
                    $message = generate_order_message($order);
                    edit_telegram_message(
                        $callback_query['message']['chat']['id'],
                        $callback_query['message']['message_id'],
                        $message['text'],
                        $message['reply_markup']
                    );

                    // إرسال إشعار للمستخدمين الآخرين
                    notify_other_users($order, $chat_id);
                } else {
                    answer_callback_query($callback_query['id'], "⛔ غير مصرح لك بتغيير حالة الطلب");
                }
            }
        }
    }

    return new WP_REST_Response(['status' => 'success'], 200);
}

// دالة تنسيق السعر للتيليجرام
function format_telegram_price($amount) {
    if (empty($amount) || $amount == 0) {
        return '0.00 دج'; // قيمة افتراضية إذا كان السعر فارغ أو صفر
    }

    // الحصول على إعدادات العملة من WooCommerce
    $currency_code = get_woocommerce_currency();
    $currency_symbol = get_woocommerce_currency_symbol($currency_code);
    $currency_position = get_option('woocommerce_currency_pos', 'right_space');
    $thousand_separator = get_option('woocommerce_price_thousand_sep', ',');
    $decimal_separator = get_option('woocommerce_price_decimal_sep', '.');
    $decimal_places = get_option('woocommerce_price_num_decimals', 2);

    // تنسيق الرقم
    $formatted_amount = number_format(
        floatval($amount),
        intval($decimal_places),
        $decimal_separator,
        $thousand_separator
    );

    // تطبيق موضع العملة
    switch ($currency_position) {
        case 'left':
            return $currency_symbol . $formatted_amount;
        case 'right':
            return $formatted_amount . $currency_symbol;
        case 'left_space':
            return $currency_symbol . ' ' . $formatted_amount;
        case 'right_space':
        default:
            return $formatted_amount . ' ' . $currency_symbol;
    }
}

// إنشاء رسالة الطلب
function generate_order_message($order) {
    // الحصول على القالب المخصص
    $options = get_option('telegram_settings');
    $template = isset($options['message_template']) ? $options['message_template'] : get_default_message_template();

    // إعداد بيانات الطلب
    $items_details = "";
    foreach ($order->get_items() as $item) {
        $items_details .= "📦 {$item->get_name()} x {$item->get_quantity()}\n";
    }

    // الحصول على بيانات العنوان
    $state_code = $order->get_shipping_state() ?: $order->get_billing_state();
    $country_code = $order->get_shipping_country() ?: $order->get_billing_country();
    $state_name = WC()->countries->get_states($country_code)[$state_code] ?? $state_code;
    $country_name = WC()->countries->get_countries()[$country_code] ?? $country_code;

    // إصلاح مشكلة السعر الإجمالي
    $formatted_total = format_telegram_price($order->get_total());

    // تخصيص عرض حالة الطلب
    $current_status = $order->get_status();
    $status_name = $current_status === 'pending' ? 'قيد الإنتظار' : wc_get_order_status_name($current_status);

    // الحصول على البلدية من حقول إضافية أو العنوان
    $shipping_city = $order->get_shipping_city() ?: $order->get_billing_city();

    // إعداد المتغيرات للاستبدال
    $variables = [
        '{order_number}' => $order->get_order_number(),
        '{customer_name}' => trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
        '{customer_phone}' => $order->get_billing_phone() ?: 'غير محدد',
        '{customer_email}' => $order->get_billing_email() ?: 'غير محدد',
        '{shipping_country}' => $country_name ?: 'غير محدد',
        '{shipping_state}' => $state_name ?: 'غير محدد',
        '{shipping_city}' => $shipping_city ?: 'غير محدد',
        '{shipping_address}' => ($order->get_shipping_address_1() ?: $order->get_billing_address_1()) ?: 'غير محدد',
        '{shipping_method}' => $order->get_shipping_method() ?: 'لم يتم تحديد طريقة التوصيل',
        '{payment_method}' => $order->get_payment_method_title() ?: 'غير محدد',
        '{order_total}' => $formatted_total,
        '{order_items}' => rtrim($items_details),
        '{order_status}' => $status_name,
        '{order_date}' => $order->get_date_created()->date_i18n('Y-m-d H:i:s'),
        '{order_notes}' => $order->get_customer_note() ?: 'لا توجد ملاحظات'
    ];

    // استبدال المتغيرات في القالب
    $text = str_replace(array_keys($variables), array_values($variables), $template);

    $reply_markup = [
        'inline_keyboard' => [
            [
                ['text' => '✅ تم التأكيد', 'callback_data' => "order_status_{$order->get_id()}_pending"],
                ['text' => '🚚 تم الشحن', 'callback_data' => "order_status_{$order->get_id()}_completed"],
                ['text' => '🚫 ملغي', 'callback_data' => "order_status_{$order->get_id()}_cancelled"]
            ]
        ]
    ];

    return [
        'text' => $text,
        'reply_markup' => $reply_markup
    ];
}

// إرسال رد على الضغط على الزر
function answer_callback_query($callback_query_id, $text) {
    $options = get_option('telegram_settings');
    $bot_token = $options['bot_token'] ?? '';

    wp_remote_post("https://api.telegram.org/bot{$bot_token}/answerCallbackQuery", [
        'body' => json_encode([
            'callback_query_id' => $callback_query_id,
            'text' => $text,
            'show_alert' => true
        ]),
        'headers' => ['Content-Type' => 'application/json']
    ]);
}

// تحديث رسالة
function edit_telegram_message($chat_id, $message_id, $text, $reply_markup) {
    $options = get_option('telegram_settings');
    $bot_token = $options['bot_token'] ?? '';

    wp_remote_post("https://api.telegram.org/bot{$bot_token}/editMessageText", [
        'body' => json_encode([
            'chat_id' => $chat_id,
            'message_id' => $message_id,
            'text' => $text,
            'parse_mode' => 'Markdown',
            'reply_markup' => $reply_markup
        ]),
        'headers' => ['Content-Type' => 'application/json']
    ]);
}

// إشعار المستخدمين الآخرين بتغيير الحالة
function notify_other_users($order, $exclude_chat_id) {
    $options = get_option('telegram_settings');
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
    $message = generate_order_message($order);
    $order_id = $order->get_id();

    if (isset($options['rotate_notifications']) && $options['rotate_notifications']) {
        // الحصول على المستخدم المرتبط بالطلب
        $order_user_map = get_option('telegram_order_user_map', []);

        if (isset($order_user_map[$order_id])) {
            $assigned_user = $order_user_map[$order_id];

            // إرسال الإشعار للمستخدم المرتبط بالطلب إذا كان مختلفاً عن المستخدم الحالي
            if ($assigned_user != $exclude_chat_id) {
                send_telegram_message($assigned_user, $message);

                // تسجيل الإشعار في السجل
                log_notification($order_id, $assigned_user, 'status_update');
            }
        } else {
            // إذا لم يكن الطلب مرتبطاً بمستخدم، نستخدم المستخدم التالي في الدورة
            if ($next_user = get_next_rotation_user($order_id)) {
                if ($next_user != $exclude_chat_id) {
                    send_telegram_message($next_user, $message);

                    // تسجيل الإشعار في السجل
                    log_notification($order_id, $next_user, 'status_update');
                }
            }
        }
    } else {
        // إرسال لجميع المستخدمين ما عدا المستخدم الذي غير الحالة
        foreach ($chat_ids as $chat_id) {
            if ($chat_id != $exclude_chat_id) {
                send_telegram_message($chat_id, $message);
            }
        }
    }
}

// إرسال رسالة تيليجرام
function send_telegram_message($chat_id, $message) {
    $options = get_option('telegram_settings');
    $bot_token = $options['bot_token'] ?? '';

    return wp_remote_post("https://api.telegram.org/bot{$bot_token}/sendMessage", [
        'body' => json_encode([
            'chat_id' => $chat_id,
            'text' => $message['text'],
            'parse_mode' => 'Markdown',
            'reply_markup' => $message['reply_markup']
        ]),
        'headers' => ['Content-Type' => 'application/json']
    ]);
}

// الحصول على المستخدم التالي في نظام التوزيع الدوري
function get_next_rotation_user($order_id = null) {
    $options = get_option('telegram_settings');
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];

    if (empty($chat_ids)) {
        return false;
    }

    // تصفية المستخدمين غير المتاحين
    $available_users = [];
    $user_availability = get_option('telegram_user_availability', []);

    foreach ($chat_ids as $chat_id) {
        // إذا لم يتم تعيين حالة المستخدم، نعتبره متاح افتراضياً
        if (!isset($user_availability[$chat_id]) || $user_availability[$chat_id]) {
            $available_users[] = $chat_id;
        }
    }

    // إذا لم يكن هناك مستخدمين متاحين، نستخدم جميع المستخدمين
    if (empty($available_users)) {
        $available_users = $chat_ids;
    }

    // إذا كان هناك معرف طلب، نتحقق إذا كان هذا الطلب مرتبط بمستخدم محدد
    if ($order_id) {
        $order_user_map = get_option('telegram_order_user_map', []);
        if (isset($order_user_map[$order_id]) && in_array($order_user_map[$order_id], $chat_ids)) {
            // إرجاع المستخدم المرتبط بالطلب
            return $order_user_map[$order_id];
        }
    }

    // الحصول على آخر مستخدم تم إرسال إشعار له
    $last_user_index = get_option('telegram_last_user_index', -1);

    // الحصول على أولويات المستخدمين
    $user_priorities = get_option('telegram_user_priorities', []);

    // ترتيب المستخدمين حسب الأولوية (الأعلى أولاً)
    usort($available_users, function($a, $b) use ($user_priorities) {
        $priority_a = isset($user_priorities[$a]) ? intval($user_priorities[$a]) : 1;
        $priority_b = isset($user_priorities[$b]) ? intval($user_priorities[$b]) : 1;
        return $priority_b - $priority_a; // ترتيب تنازلي
    });

    // حساب مؤشر المستخدم التالي
    $next_index = 0;

    // إذا كان هناك مستخدمين بأولويات مختلفة، نستخدم نظام الاحتمالات
    $total_priority = 0;
    foreach ($available_users as $user) {
        $priority = isset($user_priorities[$user]) ? intval($user_priorities[$user]) : 1;
        $total_priority += $priority;
    }

    if ($total_priority > count($available_users)) {
        // استخدام نظام الاحتمالات المرجحة
        $random = mt_rand(1, $total_priority);
        $cumulative = 0;

        foreach ($available_users as $index => $user) {
            $priority = isset($user_priorities[$user]) ? intval($user_priorities[$user]) : 1;
            $cumulative += $priority;

            if ($random <= $cumulative) {
                $next_index = $index;
                break;
            }
        }
    } else {
        // استخدام النظام الدوري العادي
        $current_index = array_search($chat_ids[$last_user_index], $available_users);
        if ($current_index !== false) {
            $next_index = ($current_index + 1) % count($available_users);
        }
    }

    $next_user = $available_users[$next_index];
    $next_user_global_index = array_search($next_user, $chat_ids);

    // تحديث المؤشر في قاعدة البيانات
    update_option('telegram_last_user_index', $next_user_global_index);

    // إذا كان هناك معرف طلب، نحفظ ارتباط الطلب بالمستخدم
    if ($order_id) {
        $order_user_map = get_option('telegram_order_user_map', []);
        $order_user_map[$order_id] = $next_user;
        update_option('telegram_order_user_map', $order_user_map);
    }

    return $next_user;
}

// معالجة الطلبات الجديدة
add_action('woocommerce_checkout_order_processed', 'handle_new_order', 10, 1);
function handle_new_order($order_id) {
    $order = wc_get_order($order_id);
    if (!$order) return;

    $options = get_option('telegram_settings');
    $selected_statuses = isset($options['order_statuses']) ? $options['order_statuses'] : ['processing'];
    $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];

    if (in_array($order->get_status(), $selected_statuses)) {
        $message = generate_order_message($order);

        // التحقق من تفعيل نظام التوزيع الدوري
        if (isset($options['rotate_notifications']) && $options['rotate_notifications']) {
            // إرسال الإشعار للمستخدم التالي في الدورة
            if ($next_user = get_next_rotation_user($order_id)) {
                send_telegram_message($next_user, $message);

                // تسجيل الإشعار في السجل
                log_notification($order_id, $next_user, 'new_order');
            }
        } else {
            // إرسال لجميع المستخدمين إذا كان نظام التوزيع غير مفعل
            foreach ($chat_ids as $chat_id) {
                send_telegram_message($chat_id, $message);
            }
        }
    }
}

// تسجيل الإشعارات
function log_notification($order_id, $user_id, $type = 'new_order') {
    $notifications_log = get_option('telegram_notifications_log', []);

    $notifications_log[] = [
        'order_id' => $order_id,
        'user_id' => $user_id,
        'type' => $type,
        'timestamp' => current_time('timestamp')
    ];

    // الاحتفاظ بآخر 100 إشعار فقط
    if (count($notifications_log) > 100) {
        $notifications_log = array_slice($notifications_log, -100);
    }

    update_option('telegram_notifications_log', $notifications_log);
}

// دالة مساعدة للحصول على اسم المستخدم
function get_telegram_user_name($chat_id) {
    $telegram_users = get_option('telegram_users_info', []);
    $user_name = '';

    if (isset($telegram_users[$chat_id])) {
        if (!empty($telegram_users[$chat_id]['full_name'])) {
            $user_name = $telegram_users[$chat_id]['full_name'];
        } elseif (!empty($telegram_users[$chat_id]['username'])) {
            $user_name = '@' . $telegram_users[$chat_id]['username'];
        }
    }

    return $user_name;
}

// إضافة معالجات Ajax
add_action('wp_ajax_reset_telegram_rotation', function() {
    check_ajax_referer('reset_telegram_rotation', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    update_option('telegram_last_user_index', -1);
    wp_send_json_success();
});

add_action('wp_ajax_clear_telegram_notifications_log', function() {
    check_ajax_referer('clear_telegram_notifications_log', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    update_option('telegram_notifications_log', []);
    wp_send_json_success();
});

add_action('wp_ajax_save_telegram_user_availability', function() {
    check_ajax_referer('save_telegram_user_availability', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    $availability = isset($_POST['availability']) ? $_POST['availability'] : [];
    update_option('telegram_user_availability', $availability);
    wp_send_json_success();
});

add_action('wp_ajax_save_telegram_user_priorities', function() {
    check_ajax_referer('save_telegram_user_priorities', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    $priorities = isset($_POST['priorities']) ? $_POST['priorities'] : [];
    update_option('telegram_user_priorities', $priorities);
    wp_send_json_success();
});

add_action('wp_ajax_save_telegram_user_info', function() {
    check_ajax_referer('save_telegram_user_info', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    $user_info = isset($_POST['user_info']) ? $_POST['user_info'] : [];
    $telegram_users = get_option('telegram_users_info', []);

    foreach ($user_info as $chat_id => $info) {
        if (!isset($telegram_users[$chat_id])) {
            $telegram_users[$chat_id] = [];
        }

        $telegram_users[$chat_id]['first_name'] = sanitize_text_field($info['first_name'] ?? '');
        $telegram_users[$chat_id]['last_name'] = sanitize_text_field($info['last_name'] ?? '');
        $telegram_users[$chat_id]['username'] = sanitize_text_field($info['username'] ?? '');
        $telegram_users[$chat_id]['full_name'] = sanitize_text_field($info['full_name'] ?? '');
    }

    update_option('telegram_users_info', $telegram_users);
    wp_send_json_success();
});

// معالجة AJAX لحفظ جميع بيانات المستخدمين
add_action('wp_ajax_save_telegram_all_users', function() {
    check_ajax_referer('save_telegram_all_users', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
        return;
    }

    // الحصول على البيانات
    $chat_ids = isset($_POST['chat_ids']) ? sanitize_text_field($_POST['chat_ids']) : '';
    $user_info = isset($_POST['user_info']) ? $_POST['user_info'] : [];
    $user_availability = isset($_POST['user_availability']) ? $_POST['user_availability'] : [];
    $user_priorities = isset($_POST['user_priorities']) ? $_POST['user_priorities'] : [];

    // تسجيل البيانات للتصحيح
    error_log('Received chat_ids: ' . print_r($chat_ids, true));
    error_log('Received user_info: ' . print_r($user_info, true));

    try {
        // تحديث معرفات المستخدمين في الإعدادات الأساسية
        $options = get_option('telegram_settings', []);
        $options['chat_ids'] = $chat_ids;
        update_option('telegram_settings', $options);

        // تحديث معلومات المستخدمين
        $telegram_users = get_option('telegram_users_info', []);
        foreach ($user_info as $chat_id => $info) {
            if (!isset($telegram_users[$chat_id])) {
                $telegram_users[$chat_id] = [];
            }

            // تأكد من أن المفتاح 'full_name' موجود
            if (isset($info['full_name'])) {
                $telegram_users[$chat_id]['full_name'] = sanitize_text_field($info['full_name']);
            }

            // الحفاظ على اسم المستخدم إذا كان موجوداً
            $telegram_users[$chat_id]['username'] = isset($telegram_users[$chat_id]['username']) ? $telegram_users[$chat_id]['username'] : '';
        }
        update_option('telegram_users_info', $telegram_users);

        // تحديث توفر المستخدمين
        update_option('telegram_user_availability', $user_availability);

        // تحديث أولويات المستخدمين
        update_option('telegram_user_priorities', $user_priorities);

        wp_send_json_success('تم حفظ البيانات بنجاح');
    } catch (Exception $e) {
        error_log('Error in save_telegram_all_users: ' . $e->getMessage());
        wp_send_json_error('حدث خطأ أثناء حفظ البيانات: ' . $e->getMessage());
    }
});

// معالج AJAX للحصول على القالب الافتراضي
add_action('wp_ajax_get_default_telegram_template', function() {
    check_ajax_referer('get_default_telegram_template', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    wp_send_json_success(get_default_message_template());
});

// معالج AJAX لمعاينة الرسالة
add_action('wp_ajax_preview_telegram_message', function() {
    check_ajax_referer('preview_telegram_message', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    $template = isset($_POST['template']) ? sanitize_textarea_field($_POST['template']) : '';

    if (empty($template)) {
        wp_send_json_error('القالب فارغ');
    }

    // إنشاء بيانات وهمية للمعاينة
    $sample_data = [
        '{order_number}' => '12345',
        '{customer_name}' => 'أحمد محمد علي',
        '{customer_phone}' => '+213555123456',
        '{customer_email}' => '<EMAIL>',
        '{shipping_country}' => 'الجزائر',
        '{shipping_state}' => 'الجزائر العاصمة',
        '{shipping_city}' => 'سيدي امحمد',
        '{shipping_address}' => 'شارع الاستقلال، حي البدر، رقم 123',
        '{shipping_method}' => 'توصيل سريع (24 ساعة)',
        '{payment_method}' => 'الدفع عند الاستلام',
        '{order_total}' => '2,500.00 دج',
        '{order_items}' => "📦 هاتف ذكي x 1\n📦 غطاء حماية x 1\n📦 شاحن سريع x 1",
        '{order_status}' => 'قيد المعالجة',
        '{order_date}' => date_i18n('Y-m-d H:i:s'),
        '{order_notes}' => 'يرجى التوصيل بعد الساعة 2 ظهراً والاتصال قبل الوصول'
    ];

    $preview = str_replace(array_keys($sample_data), array_values($sample_data), $template);
    wp_send_json_success($preview);
});

// معالج AJAX لحفظ قالب الرسالة
add_action('wp_ajax_save_telegram_message_template', function() {
    check_ajax_referer('save_telegram_message_template', 'nonce');

    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }

    $template = isset($_POST['template']) ? sanitize_textarea_field($_POST['template']) : '';

    if (empty($template)) {
        wp_send_json_error('القالب لا يمكن أن يكون فارغاً');
    }

    // حفظ القالب في الإعدادات
    $options = get_option('telegram_settings', []);
    $options['message_template'] = $template;

    if (update_option('telegram_settings', $options)) {
        wp_send_json_success('تم حفظ قالب الرسالة بنجاح');
    } else {
        wp_send_json_error('حدث خطأ أثناء حفظ القالب');
    }
});

// معالجة تغيير حالة الطلب
add_action('woocommerce_order_status_changed', 'handle_order_status_change', 10, 3);
function handle_order_status_change($order_id, $old_status, $new_status) {
    $options = get_option('telegram_settings');
    $selected_statuses = isset($options['order_statuses']) ? $options['order_statuses'] : ['processing'];

    if (in_array($new_status, $selected_statuses)) {
        $order = wc_get_order($order_id);
        if ($order) {
            $message = generate_order_message($order);

            // التحقق من تفعيل نظام التوزيع الدوري
            if (isset($options['rotate_notifications']) && $options['rotate_notifications']) {
                // إرسال الإشعار للمستخدم المرتبط بالطلب
                if ($assigned_user = get_next_rotation_user($order_id)) {
                    send_telegram_message($assigned_user, $message);

                    // تسجيل الإشعار في السجل
                    log_notification($order_id, $assigned_user, 'status_change');
                }
            } else {
                // إرسال لجميع المستخدمين إذا كان نظام التوزيع غير مفعل
                $chat_ids = isset($options['chat_ids']) ? array_map('trim', explode(',', $options['chat_ids'])) : [];
                foreach ($chat_ids as $chat_id) {
                    send_telegram_message($chat_id, $message);
                }
            }
        }
    }
}