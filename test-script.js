document.addEventListener('DOMContentLoaded', function() {
    const testButton = document.getElementById('test-telegram');
    const resultSpan = document.getElementById('test-result');
    
    if (testButton) {
        testButton.addEventListener('click', function() {
            // إظهار حالة التحميل
            testButton.disabled = true;
            resultSpan.style.display = 'inline';
            resultSpan.textContent = '🔄 جاري الاختبار...';
            
            // إرسال طلب الاختبار
            fetch(telegramTest.ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'test_telegram_notification',
                    nonce: telegramTest.nonce
                })
            })
            .then(response => response.json())
            .then(data => {
                testButton.disabled = false;
                if (data.success) {
                    resultSpan.style.color = '#28a745';
                    resultSpan.textContent = '✅ ' + data.data;
                } else {
                    resultSpan.style.color = '#dc3545';
                    resultSpan.textContent = '❌ ' + data.data;
                }
                
                // إخفاء الرسالة بعد 5 ثواني
                setTimeout(() => {
                    resultSpan.style.display = 'none';
                }, 5000);
            })
            .catch(error => {
                testButton.disabled = false;
                resultSpan.style.color = '#dc3545';
                resultSpan.textContent = '❌ حدث خطأ أثناء الاختبار';
                console.error('Error:', error);
                
                setTimeout(() => {
                    resultSpan.style.display = 'none';
                }, 5000);
            });
        });
    }
});