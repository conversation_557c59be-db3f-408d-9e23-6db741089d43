# إضافة إشعارات WooCommerce Telegram - الإصدار المحدث

## التحديثات الجديدة

### ✨ الميزات المضافة

#### 1. تخصيص الرسائل بالكامل
- **تبويب جديد**: "تخصيص الرسائل" في لوحة الإدارة
- **قالب قابل للتخصيص**: يمكن تعديل نص الرسالة بالكامل
- **متغيرات ديناميكية**: 14 متغير مختلف لعرض بيانات الطلب
- **معاينة مباشرة**: إمكانية معاينة الرسالة قبل الحفظ
- **استعادة القالب الافتراضي**: زر لاستعادة القالب الأصلي

#### 2. إصلاح مشكلة السعر الإجمالي
- **تنسيق محسن للأسعار**: يدعم جميع إعدادات العملة في WooCommerce
- **دعم العملات المختلفة**: يعمل مع أي عملة مُعرفة في WooCommerce
- **مواضع العملة**: يدعم جميع مواضع العملة (يمين، يسار، مع مسافة، بدون مسافة)
- **فواصل الآلاف والعشرية**: يحترم إعدادات WooCommerce

### 📝 المتغيرات المتاحة للتخصيص

| المتغير | الوصف | مثال |
|---------|--------|-------|
| `{order_number}` | رقم الطلب | 12345 |
| `{customer_name}` | اسم العميل | أحمد محمد |
| `{customer_phone}` | رقم الهاتف | +213555123456 |
| `{customer_email}` | البريد الإلكتروني | <EMAIL> |
| `{shipping_country}` | الولاية | الجزائر |
| `{shipping_state}` | البلدية | الجزائر العاصمة |
| `{shipping_address}` | العنوان | شارع الاستقلال، حي البدر |
| `{shipping_method}` | طريقة التوصيل | توصيل سريع |
| `{payment_method}` | طريقة الدفع | الدفع عند الاستلام |
| `{order_total}` | المجموع الإجمالي | 2,500.00 دج |
| `{order_items}` | قائمة المنتجات | 📦 منتج 1 x 2 |
| `{order_status}` | حالة الطلب | قيد المعالجة |
| `{order_date}` | تاريخ الطلب | 2024-01-15 14:30:25 |
| `{order_notes}` | ملاحظات الطلب | يرجى التوصيل بعد الساعة 2 |

### 🎯 كيفية الاستخدام

#### تخصيص الرسائل:
1. اذهب إلى **الإعدادات** → **إشعارات Telegram**
2. انقر على تبويب **"تخصيص الرسائل"**
3. عدّل القالب باستخدام المتغيرات المتاحة
4. انقر على **"معاينة الرسالة"** لرؤية النتيجة
5. احفظ التغييرات بالنقر على **"حفظ قالب الرسالة"**

#### مثال على قالب مخصص:
```
🛒 *طلب جديد #{order_number}*

👤 *العميل:* {customer_name}
📱 *الهاتف:* {customer_phone}
📍 *العنوان:* {shipping_address}, {shipping_state}

💰 *المجموع:* {order_total}

📦 *المنتجات:*
{order_items}

📋 *الحالة:* {order_status}
📅 *التاريخ:* {order_date}

💬 *ملاحظات:* {order_notes}
```

### 🔧 الإصلاحات

#### مشكلة السعر الإجمالي:
- **المشكلة**: كان السعر يظهر 0دج في بعض الحالات
- **الحل**: دالة جديدة `format_telegram_price()` تتعامل مع:
  - الأسعار الفارغة أو الصفرية
  - إعدادات العملة المختلفة
  - فواصل الآلاف والعشرية
  - مواضع رمز العملة

#### تحسينات أخرى:
- **أداء محسن**: تحسين في استعلامات قاعدة البيانات
- **أمان معزز**: التحقق من صحة البيانات المدخلة
- **واجهة محسنة**: تصميم أفضل لقسم تخصيص الرسائل

### 🧪 أدوات التطوير

#### ملف الاختبار:
- يتم تحميل `test-message-customization.php` في وضع التطوير
- يوفر صفحة اختبار في **الإعدادات** → **اختبار Telegram**
- يعرض:
  - إعدادات WooCommerce الحالية
  - اختبار تنسيق الأسعار
  - معاينة المتغيرات المتاحة
  - اختبار القالب مع بيانات وهمية

### 📋 متطلبات التشغيل

- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+
- إعدادات SSL للموقع (لـ Webhook)

### 🚀 التثبيت والتفعيل

1. ارفع ملفات الإضافة إلى مجلد `/wp-content/plugins/`
2. فعّل الإضافة من لوحة إدارة WordPress
3. اذهب إلى **الإعدادات** → **إشعارات Telegram**
4. أدخل بيانات البوت ومعرفات المستخدمين
5. خصص قالب الرسالة حسب احتياجاتك

### 🔄 الترقية من الإصدار السابق

- **تلقائية**: القالب الافتراضي سيتم تطبيقه تلقائياً
- **الإعدادات**: جميع الإعدادات السابقة محفوظة
- **التوافق**: متوافق مع جميع الإعدادات السابقة

### 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- تحقق من ملف `test-message-customization.php` للتشخيص
- راجع إعدادات WooCommerce للعملة
- تأكد من صحة بيانات البوت

### 📝 ملاحظات مهمة

1. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية قبل التحديث
2. **الاختبار**: استخدم ميزة المعاينة قبل حفظ القالب
3. **الأمان**: لا تشارك رمز البوت مع أشخاص غير مصرح لهم
4. **الأداء**: القالب المخصص قد يؤثر على سرعة الإرسال إذا كان طويلاً جداً

---

**الإصدار**: 1.12  
**تاريخ التحديث**: 2024-01-15  
**المطور**: فريق التطوير
