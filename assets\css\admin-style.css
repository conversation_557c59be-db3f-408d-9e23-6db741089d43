/* تنسيقات إدارة إشعارات تيليجرام */

/* تنسيقات عامة */
.telegram-admin-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    padding: 20px;
    border-top: 3px solid #0088cc;
}

.telegram-admin-section h3 {
    margin-top: 0;
    color: #0088cc;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.telegram-admin-section .description {
    color: #666;
    margin-bottom: 15px;
    font-size: 13px;
    line-height: 1.5;
}

/* تنسيقات الجداول */
.telegram-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    border: 1px solid #e5e5e5;
}

.telegram-table th {
    background-color: #f9f9f9;
    text-align: right;
    padding: 10px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
}

.telegram-table td {
    padding: 10px;
    border-bottom: 1px solid #e5e5e5;
    vertical-align: middle;
}

.telegram-table tr:last-child td {
    border-bottom: none;
}

.telegram-table tr:hover {
    background-color: #f5f5f5;
}

.telegram-table .user-name {
    color: #0073aa;
    font-weight: 500;
}

.telegram-table .unknown-user {
    color: #999;
    font-style: italic;
}

/* تنسيقات الأزرار */
.telegram-button {
    background-color: #0088cc;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.telegram-button:hover {
    background-color: #006699;
}

.telegram-button:focus {
    outline: none;
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px #0088cc;
}

/* تنسيقات حقول الإدخال */
.telegram-input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 8px;
    width: 100%;
    box-sizing: border-box;
}

.telegram-input:focus {
    border-color: #0088cc;
    box-shadow: 0 0 0 1px #0088cc;
    outline: none;
}

.telegram-checkbox {
    margin-right: 0;
}

.telegram-number-input {
    width: 60px;
    text-align: center;
}

/* تنسيقات الإشعارات */
.telegram-notice {
    background-color: #f8f9fa;
    border-right: 3px solid #0088cc;
    padding: 10px 15px;
    margin: 15px 0;
    border-radius: 4px;
}

.telegram-notice.success {
    border-right-color: #46b450;
    background-color: #f7fff7;
}

.telegram-notice.warning {
    border-right-color: #ffb900;
    background-color: #fffbf1;
}

.telegram-notice.error {
    border-right-color: #dc3232;
    background-color: #fef7f7;
}

/* تنسيقات خاصة بالتوزيع الدوري */
.next-user-box {
    background: #f0f7fc;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.next-user-box code {
    background: #fff;
    padding: 5px 10px;
    border-radius: 3px;
    margin-left: 10px;
    font-size: 14px;
    border: 1px solid #ddd;
}

.next-user-box .user-name {
    margin-right: 10px;
    font-weight: 500;
}

/* تنسيقات الأقسام المختلفة */
.user-availability-section,
.user-priorities-section,
.user-info-section,
.rotation-status-section {
    margin-bottom: 30px;
}

/* تنسيقات الأيقونات */
.telegram-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px;
}

/* تنسيقات التبويبات */
.telegram-tabs {
    display: flex;
    border-bottom: 1px solid #ccc;
    margin-bottom: 20px;
}

.telegram-tab {
    padding: 10px 15px;
    cursor: pointer;
    margin-bottom: -1px;
    border: 1px solid transparent;
    border-bottom: none;
    background-color: #f9f9f9;
    margin-left: 5px;
    border-radius: 4px 4px 0 0;
    transition: all 0.2s ease;
}

.telegram-tab:hover {
    background-color: #f0f0f1;
}

.telegram-tab.active {
    background-color: #fff;
    border-color: #ccc;
    border-bottom-color: #fff;
    font-weight: 600;
    color: #0088cc;
}

.telegram-tab-content {
    display: none;
}

.telegram-tab-content.active {
    display: block;
}

/* تنسيقات التبويبات الفرعية */
.user-tabs-content {
    padding: 15px 0;
}

.user-tab-content {
    display: none;
}

.user-tab-content.active {
    display: block;
}

#user-management-tabs {
    margin-bottom: 0;
}

#user-management-tabs .telegram-tab {
    font-size: 13px;
    padding: 8px 12px;
}

/* تنسيقات الرسائل */
.telegram-message {
    padding: 10px 15px;
    margin: 10px 0;
    border-radius: 4px;
    background-color: #f8f9fa;
    border-right: 3px solid #0088cc;
}

.telegram-message.success {
    background-color: #f7fff7;
    border-right-color: #46b450;
}

.telegram-message.error {
    background-color: #fef7f7;
    border-right-color: #dc3232;
}

/* تنسيقات التحميل */
.telegram-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 136, 204, 0.2);
    border-radius: 50%;
    border-top-color: #0088cc;
    animation: telegram-spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes telegram-spin {
    to {
        transform: rotate(360deg);
    }
}

/* تنسيقات تخصيص الرسائل */
.message-customization-section {
    margin-bottom: 30px;
}

.message-customization-section textarea {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    min-height: 300px;
}

.message-customization-section .telegram-notice {
    margin-bottom: 20px;
}

.message-customization-section code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
    color: #d63384;
    font-weight: 500;
}

#message-preview {
    border: 1px solid #ddd;
    border-radius: 4px;
}

#preview-content {
    max-height: 400px;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.6;
    color: #333;
}

/* تنسيقات شبكة المتغيرات */
.message-customization-section .telegram-notice > div {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 8px;
}

.message-customization-section .telegram-notice > div > div {
    padding: 5px;
    background: #fff;
    border-radius: 3px;
    border: 1px solid #e0e0e0;
    font-size: 12px;
}

/* تنسيقات الإحصائيات المحسنة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stats-title {
    color: #6c757d;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-value {
    color: #0088cc;
    font-size: 28px;
    font-weight: 700;
    line-height: 1;
}

/* تنسيقات جدول الإحصائيات */
.telegram-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.telegram-table th {
    background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
    color: #fff;
    padding: 12px 15px;
    text-align: right;
    font-weight: 600;
    font-size: 13px;
    border: none;
}

.telegram-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.telegram-table tbody tr:hover {
    background-color: #f8f9fa;
}

.telegram-table tbody tr:last-child td {
    border-bottom: none;
}

/* تنسيقات شارات الحالة */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
    border: 1px solid transparent;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.status-badge.processing {
    background-color: #cce5ff;
    color: #004085;
    border-color: #74b9ff;
}

.status-badge.completed {
    background-color: #d4edda;
    color: #155724;
    border-color: #00b894;
}

.status-badge.cancelled {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #e17055;
}

/* تنسيقات أسماء المستخدمين */
.user-name {
    display: block;
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
    margin-top: 2px;
}

/* تنسيقات التحذيرات */
.telegram-notice.warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 6px;
    border-right: 4px solid #f39c12;
    margin: 15px 0;
}

/* تنسيقات شريط التقدم المحسن */
.percentage-bar {
    position: relative;
    width: 120px;
    height: 24px;
    background: linear-gradient(135deg, #f1f2f6 0%, #e9ecef 100%);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.percentage-fill {
    height: 100%;
    background: linear-gradient(135deg, #0088cc 0%, #74b9ff 100%);
    transition: width 0.6s ease;
    border-radius: 12px;
}

.percentage-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: #2d3436;
    text-shadow: 0 1px 2px rgba(255,255,255,0.8);
}

/* تنسيقات الواجهة الجديدة */
.telegram-settings-section {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.telegram-settings-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #0088cc;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #0088cc;
    padding-bottom: 8px;
}

/* تنسيقات التبويبات المحسنة */
.telegram-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 20px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.telegram-tab {
    flex: 1;
    padding: 12px 20px;
    text-align: center;
    cursor: pointer;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #6c757d;
    border: 1px solid transparent;
}

.telegram-tab:hover {
    background: rgba(0, 136, 204, 0.1);
    color: #0088cc;
}

.telegram-tab.active {
    background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
    color: #fff;
    box-shadow: 0 2px 4px rgba(0, 136, 204, 0.3);
    border-color: #0066aa;
}

/* تنسيقات المحتوى */
.telegram-tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.telegram-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيقات الإحصائيات المحسنة */
.statistics-section .telegram-tabs {
    background: #e9ecef;
    border: 1px solid #dee2e6;
}

.statistics-section .telegram-tab {
    font-size: 14px;
    padding: 10px 16px;
}

.statistics-section .telegram-tab.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #20c997;
}

/* تنسيقات الإشعارات */
.notification-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
    border: 1px solid transparent;
}

.notification-badge.new-order {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-color: #b8daff;
}

.notification-badge.status-update {
    background: linear-gradient(135deg, #cce5ff 0%, #b3d7ff 100%);
    color: #004085;
    border-color: #74b9ff;
}

/* تنسيقات الأزرار المحسنة */
.telegram-button {
    background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
    border: 1px solid #0066aa;
    color: #fff;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.telegram-button:hover {
    background: linear-gradient(135deg, #0066aa 0%, #004d82 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 136, 204, 0.3);
    color: #fff;
}

.telegram-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 136, 204, 0.3);
}

/* تنسيقات الجداول المحسنة */
.telegram-table {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.telegram-table th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: #fff;
    font-weight: 600;
    text-align: center;
    padding: 14px 12px;
    border: none;
}

.telegram-table td {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
    text-align: center;
}

.telegram-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.telegram-table tbody tr:hover {
    background-color: #e3f2fd;
    transition: background-color 0.2s ease;
}

/* تنسيقات النماذج */
.form-table th {
    font-weight: 600;
    color: #495057;
    padding: 15px 10px;
}

.form-table td {
    padding: 15px 10px;
}

/* تنسيقات التحذيرات المحسنة */
.telegram-notice {
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
    border-right: 4px solid;
}

.telegram-notice.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #f39c12;
    color: #856404;
}

.telegram-notice.info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-color: #17a2b8;
    color: #0c5460;
}

.telegram-notice.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
    color: #155724;
}
