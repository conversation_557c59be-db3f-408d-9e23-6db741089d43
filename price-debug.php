<?php
/**
 * ملف تشخيص مشاكل الأسعار في إضافة Telegram
 * يساعد في تحديد سبب عدم ظهور الأسعار بشكل صحيح
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// دالة تشخيص مشاكل الأسعار
function debug_telegram_price_issues() {
    echo "<div class='wrap'>";
    echo "<h1>🔍 تشخيص مشاكل الأسعار - Telegram Notifications</h1>";
    echo "<div style='background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);'>";
    
    // 1. فحص إعدادات WooCommerce
    echo "<h3>📊 إعدادات WooCommerce:</h3>";
    
    $currency_settings = [
        'العملة' => get_woocommerce_currency(),
        'رمز العملة' => get_woocommerce_currency_symbol(),
        'موضع العملة' => get_option('woocommerce_currency_pos'),
        'فاصل الآلاف' => get_option('woocommerce_price_thousand_sep'),
        'فاصل العشرية' => get_option('woocommerce_price_decimal_sep'),
        'عدد الخانات العشرية' => get_option('woocommerce_price_num_decimals')
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
    echo "<tr style='background: #f9f9f9;'><th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>الإعداد</th><th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>القيمة</th></tr>";
    
    foreach ($currency_settings as $setting => $value) {
        $display_value = is_string($value) ? $value : var_export($value, true);
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$setting}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px; font-family: monospace;'>{$display_value}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. اختبار تنسيق الأسعار
    echo "<h3>💰 اختبار تنسيق الأسعار:</h3>";
    
    $test_amounts = [0, 1500, 2500.50, 4500, 10000];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
    echo "<tr style='background: #f9f9f9;'><th style='border: 1px solid #ddd; padding: 10px;'>المبلغ الأصلي</th><th style='border: 1px solid #ddd; padding: 10px;'>WooCommerce</th><th style='border: 1px solid #ddd; padding: 10px;'>Telegram</th><th style='border: 1px solid #ddd; padding: 10px;'>الحالة</th></tr>";
    
    foreach ($test_amounts as $amount) {
        $wc_formatted = wc_price($amount);
        $telegram_formatted = format_telegram_price($amount);
        
        $status = ($amount > 0 && strpos($telegram_formatted, '0') === 0) ? '❌ مشكلة' : '✅ صحيح';
        
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$amount}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$wc_formatted}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px; font-weight: bold;'>{$telegram_formatted}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$status}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. فحص آخر الطلبات
    echo "<h3>📋 فحص آخر الطلبات:</h3>";
    
    $recent_orders = wc_get_orders([
        'limit' => 5,
        'orderby' => 'date',
        'order' => 'DESC'
    ]);
    
    if (!empty($recent_orders)) {
        echo "<table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
        echo "<tr style='background: #f9f9f9;'><th style='border: 1px solid #ddd; padding: 10px;'>رقم الطلب</th><th style='border: 1px solid #ddd; padding: 10px;'>المجموع الأصلي</th><th style='border: 1px solid #ddd; padding: 10px;'>المجموع المنسق</th><th style='border: 1px solid #ddd; padding: 10px;'>الحالة</th></tr>";
        
        foreach ($recent_orders as $order) {
            $order_total = $order->get_total();
            $formatted_total = format_telegram_price($order_total);
            $status = ($order_total > 0 && strpos($formatted_total, '0') === 0) ? '❌ مشكلة' : '✅ صحيح';
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>#{$order->get_id()}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$order_total}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px; font-weight: bold;'>{$formatted_total}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$status}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد طلبات حديثة للفحص.</p>";
    }
    
    // 4. فحص رموز HTML
    echo "<h3>🔤 فحص رموز HTML:</h3>";
    
    $currency_symbol = get_woocommerce_currency_symbol();
    $clean_symbol = clean_telegram_text($currency_symbol);
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
    echo "<tr style='background: #f9f9f9;'><th style='border: 1px solid #ddd; padding: 10px;'>النوع</th><th style='border: 1px solid #ddd; padding: 10px;'>القيمة</th><th style='border: 1px solid #ddd; padding: 10px;'>الترميز</th></tr>";
    
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 10px;'>رمز العملة الأصلي</td>";
    echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$currency_symbol}</td>";
    echo "<td style='border: 1px solid #ddd; padding: 10px; font-family: monospace;'>" . htmlspecialchars($currency_symbol) . "</td>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<td style='border: 1px solid #ddd; padding: 10px;'>رمز العملة المنظف</td>";
    echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$clean_symbol}</td>";
    echo "<td style='border: 1px solid #ddd; padding: 10px; font-family: monospace;'>" . htmlspecialchars($clean_symbol) . "</td>";
    echo "</tr>";
    
    echo "</table>";
    
    // 5. اختبار دالة التنظيف
    echo "<h3>🧹 اختبار دالة التنظيف:</h3>";
    
    $test_strings = [
        'نص عادي',
        'نص مع &amp; رموز',
        'نص مع &#x62f;.&#x62c; رموز عربية',
        '<span>نص مع HTML</span>',
        '  نص مع مسافات زائدة  '
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
    echo "<tr style='background: #f9f9f9;'><th style='border: 1px solid #ddd; padding: 10px;'>النص الأصلي</th><th style='border: 1px solid #ddd; padding: 10px;'>النص المنظف</th></tr>";
    
    foreach ($test_strings as $test_string) {
        $cleaned = clean_telegram_text($test_string);
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 10px; font-family: monospace;'>" . htmlspecialchars($test_string) . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px; font-weight: bold;'>{$cleaned}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 6. توصيات الإصلاح
    echo "<h3>💡 توصيات الإصلاح:</h3>";
    
    $recommendations = [];
    
    if (get_woocommerce_currency() !== 'DZD') {
        $recommendations[] = "⚠️ العملة المحددة ليست الدينار الجزائري (DZD). تأكد من إعدادات العملة في WooCommerce.";
    }
    
    if (strpos(get_woocommerce_currency_symbol(), '&#x') !== false) {
        $recommendations[] = "⚠️ رمز العملة يحتوي على رموز HTML. سيتم تنظيفه تلقائياً.";
    }
    
    if (empty($recommendations)) {
        $recommendations[] = "✅ جميع الإعدادات تبدو صحيحة.";
    }
    
    echo "<ul style='margin: 15px 0; padding-right: 20px;'>";
    foreach ($recommendations as $recommendation) {
        echo "<li style='margin: 5px 0;'>{$recommendation}</li>";
    }
    echo "</ul>";
    
    // 7. خطوات الإصلاح
    echo "<h3>🔧 خطوات الإصلاح المقترحة:</h3>";
    
    echo "<ol style='margin: 15px 0; padding-right: 20px;'>";
    echo "<li>تأكد من أن العملة محددة كـ DZD في <strong>WooCommerce → الإعدادات → عام</strong></li>";
    echo "<li>تأكد من أن رمز العملة محدد كـ 'دج' في نفس الصفحة</li>";
    echo "<li>تحقق من أن المنتجات لها أسعار محددة</li>";
    echo "<li>جرب إنشاء طلب اختبار وتحقق من الرسالة</li>";
    echo "<li>راجع سجل الأخطاء في <code>wp-content/debug.log</code></li>";
    echo "</ol>";
    
    echo "</div>";
    echo "</div>";
}

// إضافة صفحة التشخيص لقائمة الإدارة
add_action('admin_menu', function() {
    add_submenu_page(
        'options-general.php',
        'تشخيص أسعار Telegram',
        'تشخيص أسعار Telegram',
        'manage_options',
        'telegram-price-debug',
        'debug_telegram_price_issues'
    );
});

// دالة اختبار سريع للسعر
function quick_price_test($order_id = null) {
    if ($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return ['error' => 'الطلب غير موجود'];
        }
        
        $order_total = $order->get_total();
        $formatted_total = format_telegram_price($order_total);
        
        return [
            'order_id' => $order_id,
            'original_total' => $order_total,
            'formatted_total' => $formatted_total,
            'currency' => get_woocommerce_currency(),
            'currency_symbol' => get_woocommerce_currency_symbol()
        ];
    }
    
    return ['error' => 'لم يتم تحديد رقم الطلب'];
}

// معالج AJAX لاختبار سعر طلب محدد
add_action('wp_ajax_test_specific_order_price', function() {
    check_ajax_referer('test_specific_order_price', 'nonce');
    
    if (!current_user_can('manage_options')) {
        wp_send_json_error('غير مصرح');
    }
    
    $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
    
    if (!$order_id) {
        wp_send_json_error('رقم الطلب مطلوب');
    }
    
    $result = quick_price_test($order_id);
    
    if (isset($result['error'])) {
        wp_send_json_error($result['error']);
    } else {
        wp_send_json_success($result);
    }
});
