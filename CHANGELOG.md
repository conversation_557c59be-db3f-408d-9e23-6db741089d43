# سجل التغييرات - إضافة WooCommerce Telegram Notifications

## الإصدار 1.12.2 - 2024-01-15

### 🚨 إصلاح مشكلة الأسعار الحرجة
- **حل مشكلة السعر يظهر 0**: تحسين استخراج السعر من الطلبات
- **إصلاح الرموز الغريبة**: حل مشكلة `&#x62f;.&#x62c;` وإظهار "دج" بشكل صحيح
- **تحسين دالة التنسيق**: معالجة أفضل للأسعار الفارغة والصفرية
- **أدوات تشخيص جديدة**: صفحة تشخيص الأسعار وزر اختبار التنسيق
- **تسجيل الأخطاء**: معلومات تشخيص مفصلة في سجل الأخطاء

## الإصدار 1.12.1 - 2024-01-15

### 🔧 إصلاحات مهمة
- **تصحيح تسميات المتغيرات الجغرافية**:
  - `{shipping_country}` = الدولة (وليس الولاية)
  - `{shipping_state}` = الولاية (وليس البلدية)
  - `{shipping_city}` = البلدية (متغير جديد)

## الإصدار 1.12 - 2024-01-15

### ✨ الميزات الجديدة

#### 🎨 تخصيص الرسائل بالكامل
- **تبويب جديد**: "تخصيص الرسائل" في لوحة الإدارة
- **قالب قابل للتعديل**: إمكانية تخصيص نص الرسالة بالكامل
- **14 متغير ديناميكي**: لعرض جميع بيانات الطلب
- **معاينة مباشرة**: رؤية الرسالة قبل الحفظ
- **استعادة القالب الافتراضي**: زر لإعادة تعيين القالب

#### 🔧 إصلاح مشكلة السعر الإجمالي
- **دالة جديدة**: `format_telegram_price()` لتنسيق الأسعار
- **دعم جميع العملات**: يعمل مع أي عملة في WooCommerce
- **مواضع العملة**: دعم جميع مواضع رمز العملة
- **فواصل مخصصة**: احترام إعدادات الآلاف والعشرية
- **معالجة الأسعار الفارغة**: حل مشكلة عرض 0دج

#### 🧪 أدوات التشخيص والاختبار
- **فحص الصحة**: صفحة شاملة لفحص حالة الإضافة
- **اختبار سريع**: زر لإرسال رسالة اختبار فورية
- **صفحة التطوير**: أدوات متقدمة للمطورين
- **تشخيص الأخطاء**: معلومات مفصلة عن المشاكل

### 🔄 التحسينات

#### واجهة المستخدم
- **تصميم محسن**: تنسيق أفضل لقسم تخصيص الرسائل
- **رسائل حالة**: تأكيدات واضحة للعمليات
- **تحميل تفاعلي**: مؤشرات تحميل للعمليات الطويلة
- **تنظيم أفضل**: ترتيب منطقي للتبويبات

#### الأداء والأمان
- **تحسين الاستعلامات**: أداء أفضل لقاعدة البيانات
- **التحقق من البيانات**: فلترة وتنظيف محسن للمدخلات
- **معالجة الأخطاء**: رسائل خطأ أكثر وضوحاً
- **حفظ منفصل**: إمكانية حفظ القالب بشكل مستقل

### 📝 المتغيرات الجديدة

```
{order_number}     - رقم الطلب
{customer_name}    - اسم العميل الكامل
{customer_phone}   - رقم الهاتف
{customer_email}   - البريد الإلكتروني
{shipping_country} - الدولة
{shipping_state}   - الولاية
{shipping_city}    - البلدية
{shipping_address} - العنوان الكامل
{shipping_method}  - طريقة التوصيل
{payment_method}   - طريقة الدفع
{order_total}      - المجموع الإجمالي (مُنسق)
{order_items}      - قائمة المنتجات
{order_status}     - حالة الطلب
{order_date}       - تاريخ ووقت الطلب
{order_notes}      - ملاحظات العميل
```

### 🐛 الإصلاحات

#### مشكلة السعر الإجمالي
- **السبب**: عدم تنسيق السعر حسب إعدادات WooCommerce
- **الحل**: دالة مخصصة تحترم جميع إعدادات العملة
- **النتيجة**: عرض صحيح للأسعار بجميع العملات

#### مشاكل التنسيق
- **الرموز الخاصة**: معالجة أفضل للرموز في النصوص
- **الترميز**: حل مشاكل عرض النصوص العربية
- **المسافات**: تنسيق أفضل للمسافات والأسطر

#### مشاكل الأداء
- **الذاكرة**: تحسين استخدام الذاكرة
- **السرعة**: تحسين سرعة إرسال الرسائل
- **الاستقرار**: معالجة أفضل للأخطاء

### 📁 الملفات الجديدة

```
health-check.php              - فحص صحة الإضافة
test-message-customization.php - أدوات الاختبار والتطوير
README.md                     - دليل شامل للإضافة
QUICK_GUIDE.md               - دليل سريع للمستخدم
CHANGELOG.md                 - سجل التغييرات (هذا الملف)
```

### 🔧 التحديثات التقنية

#### دوال جديدة
- `telegram_message_customization_render()` - واجهة تخصيص الرسائل
- `get_default_message_template()` - القالب الافتراضي
- `format_telegram_price()` - تنسيق الأسعار
- `telegram_health_check()` - فحص صحة الإضافة
- `quick_message_test()` - اختبار سريع للرسائل

#### معالجات AJAX جديدة
- `get_default_telegram_template` - استعادة القالب الافتراضي
- `preview_telegram_message` - معاينة الرسالة
- `save_telegram_message_template` - حفظ القالب
- `telegram_quick_test` - اختبار سريع

#### تحسينات CSS
- تنسيقات جديدة لقسم تخصيص الرسائل
- شبكة متجاوبة للمتغيرات
- مؤشرات تحميل محسنة
- ألوان وأيقونات محدثة

### 🚀 طريقة الترقية

#### من الإصدار 1.11:
1. ارفع الملفات الجديدة
2. لا حاجة لإعادة تكوين الإعدادات
3. القالب الافتراضي سيُطبق تلقائياً
4. جميع الإعدادات السابقة محفوظة

#### التحقق من نجاح الترقية:
1. اذهب إلى **الإعدادات** → **فحص صحة Telegram**
2. تأكد من ظهور جميع العلامات الخضراء
3. جرب الاختبار السريع
4. تحقق من قسم تخصيص الرسائل

### 📋 المتطلبات

- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+
- SSL للموقع (مُوصى به)
- اتصال إنترنت مستقر

### 🔮 الخطط المستقبلية

#### الإصدار 1.13 (قريباً):
- دعم الصور في الرسائل
- قوالب متعددة حسب نوع الطلب
- إحصائيات متقدمة
- تكامل مع خدمات أخرى

#### طلبات الميزات:
- دعم رسائل صوتية
- جدولة الإشعارات
- فلترة متقدمة للطلبات
- تقارير مفصلة

### 📞 الدعم والمساعدة

#### للحصول على المساعدة:
1. راجع `QUICK_GUIDE.md` للحلول السريعة
2. استخدم صفحة فحص الصحة للتشخيص
3. تحقق من ملف `debug.log` للأخطاء
4. اتصل بفريق الدعم مع تفاصيل المشكلة

#### الإبلاغ عن مشاكل:
- وصف مفصل للمشكلة
- خطوات إعادة إنتاج المشكلة
- لقطات شاشة إن أمكن
- معلومات النظام من صفحة فحص الصحة

---

**شكراً لاستخدام إضافة WooCommerce Telegram Notifications!** 🙏
