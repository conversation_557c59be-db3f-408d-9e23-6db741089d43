# دليل إصلاح مشاكل الأسعار - Telegram Notifications

## 🚨 المشكلة المحددة

**الأعراض:**
- السعر يظهر `0` في رسائل Telegram
- ظهور رموز غريبة مثل `&#x62f;.&#x62c;` بدلاً من "دج"
- الرسالة تحتوي على معلومات صحيحة عدا السعر

**مثال على المشكلة:**
```
💰 المجموع: 0  &#x62f;.&#x62c;  
```

**النتيجة المطلوبة:**
```
💰 المجموع: 4,500.00 دج
```

## 🔧 الإصلاحات المطبقة

### 1. تحسين دالة تنسيق السعر
```php
function format_telegram_price($amount) {
    // التحقق من صحة المبلغ
    $amount = floatval($amount);
    
    if ($amount <= 0) {
        error_log('Telegram Price Error: Amount is zero or invalid: ' . $amount);
        return '0.00 دج';
    }
    
    // إصلاح مشكلة الترميز
    $clean_symbol = $currency_symbol;
    if (strpos($currency_symbol, '&#x') !== false || $currency_code === 'DZD') {
        $clean_symbol = 'دج';
    }
    
    // باقي الكود...
}
```

### 2. تحسين استخراج السعر من الطلب
```php
// إصلاح مشكلة السعر الإجمالي
$order_total = $order->get_total();

// التأكد من وجود السعر
if (empty($order_total) || $order_total == 0) {
    // محاولة الحصول على السعر من العناصر
    $items_total = 0;
    foreach ($order->get_items() as $item) {
        $items_total += $item->get_total();
    }
    
    // إضافة الشحن والضرائب
    $items_total += $order->get_shipping_total();
    $items_total += $order->get_total_tax();
    
    $order_total = $items_total > 0 ? $items_total : $order_total;
}
```

### 3. دالة تنظيف النصوص
```php
function clean_telegram_text($text) {
    // إزالة رموز HTML
    $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // إزالة العلامات HTML
    $text = strip_tags($text);
    
    // تنظيف المسافات الزائدة
    $text = trim($text);
    
    return $text;
}
```

### 4. تسجيل معلومات التشخيص
```php
// تسجيل معلومات التشخيص
error_log('Telegram Order Debug: Order ID=' . $order->get_id() . ', Total=' . $order_total . ', Status=' . $order->get_status());
error_log('Telegram Price Debug: Amount=' . $amount . ', Currency=' . $currency_code . ', Symbol=' . $currency_symbol);
```

## 🧪 أدوات التشخيص الجديدة

### 1. صفحة تشخيص الأسعار
```
الإعدادات → تشخيص أسعار Telegram
```

**الميزات:**
- فحص إعدادات WooCommerce
- اختبار تنسيق الأسعار
- فحص آخر الطلبات
- اختبار دالة التنظيف
- توصيات الإصلاح

### 2. زر اختبار تنسيق السعر
```
تخصيص الرسائل → اختبار تنسيق السعر
```

**الميزات:**
- اختبار مبالغ مختلفة
- عرض معلومات العملة
- مقارنة النتائج

### 3. تسجيل الأخطاء
```
wp-content/debug.log
```

**معلومات مسجلة:**
- معرف الطلب والمبلغ
- إعدادات العملة
- رسائل الخطأ

## 🔍 خطوات التشخيص

### 1. فحص إعدادات WooCommerce
```
WooCommerce → الإعدادات → عام → خيارات العملة
```

**تأكد من:**
- العملة: DZD (دينار جزائري)
- رمز العملة: دج
- موضع العملة: يمين مع مسافة
- فاصل الآلاف: ,
- فاصل العشرية: .

### 2. فحص أسعار المنتجات
```
المنتجات → تحرير المنتج → بيانات المنتج
```

**تأكد من:**
- السعر العادي محدد
- السعر أكبر من 0
- لا توجد رموز خاصة في السعر

### 3. اختبار طلب جديد
```
إنشاء طلب اختبار ومراقبة الرسالة
```

### 4. مراجعة سجل الأخطاء
```
wp-content/debug.log
```

**ابحث عن:**
- `Telegram Price Error`
- `Telegram Order Debug`
- `Telegram Price Debug`

## ⚠️ المشاكل الشائعة والحلول

### المشكلة 1: السعر يظهر 0
**السبب:** الطلب لا يحتوي على مبلغ إجمالي
**الحل:** 
- تحقق من أسعار المنتجات
- تأكد من حفظ الطلب بشكل صحيح
- استخدم صفحة التشخيص

### المشكلة 2: رموز HTML غريبة
**السبب:** رمز العملة يحتوي على رموز HTML
**الحل:**
- تحديث إعدادات العملة
- استخدام دالة التنظيف
- تعيين رمز العملة يدوياً

### المشكلة 3: تنسيق خاطئ للسعر
**السبب:** إعدادات العملة غير صحيحة
**الحل:**
- مراجعة إعدادات WooCommerce
- استخدام زر اختبار التنسيق
- تحديث موضع العملة

## 🎯 الحلول السريعة

### حل سريع 1: إعادة تعيين إعدادات العملة
```
WooCommerce → الإعدادات → عام
العملة: DZD
رمز العملة: دج
موضع العملة: right_space
```

### حل سريع 2: اختبار دالة التنسيق
```php
// في ملف functions.php أو كود مخصص
$test_price = format_telegram_price(4500);
echo $test_price; // يجب أن يظهر: 4,500.00 دج
```

### حل سريع 3: فحص طلب محدد
```
الإعدادات → تشخيص أسعار Telegram
أدخل رقم الطلب واختبر
```

## 📋 قائمة التحقق

- [ ] إعدادات العملة صحيحة في WooCommerce
- [ ] أسعار المنتجات محددة وأكبر من 0
- [ ] رمز العملة لا يحتوي على رموز HTML
- [ ] دالة `format_telegram_price` تعمل بشكل صحيح
- [ ] سجل الأخطاء لا يحتوي على أخطاء
- [ ] اختبار الرسالة يظهر السعر بشكل صحيح

## 🆘 إذا استمرت المشكلة

### 1. جمع معلومات التشخيص
```
الإعدادات → تشخيص أسعار Telegram
الإعدادات → فحص صحة Telegram
```

### 2. تفعيل سجل الأخطاء
```php
// في wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### 3. اختبار يدوي
```php
// كود اختبار مؤقت
$order = wc_get_order(1897); // رقم الطلب المشكل
$total = $order->get_total();
$formatted = format_telegram_price($total);
echo "الأصلي: $total, المنسق: $formatted";
```

### 4. التواصل مع الدعم
**معلومات مطلوبة:**
- رقم الطلب المشكل
- نتائج صفحة التشخيص
- محتوى سجل الأخطاء
- إعدادات العملة الحالية

---

**ملاحظة:** هذه الإصلاحات تم تطبيقها في الإصدار 1.12.1 وما بعده.
