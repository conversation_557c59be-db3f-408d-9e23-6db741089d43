<?php
/**
 * ملف اختبار لتخصيص الرسائل
 * يمكن تشغيله لاختبار وظائف تخصيص الرسائل
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// دالة اختبار تنسيق السعر
function test_price_formatting() {
    echo "<h3>اختبار تنسيق السعر:</h3>";
    
    // محاكاة طلب وهمي
    $test_amounts = [0, 1500, 2500.50, 10000];
    $currency_symbol = get_woocommerce_currency_symbol();
    $currency_position = get_option('woocommerce_currency_pos');
    
    echo "<p>رمز العملة: {$currency_symbol}</p>";
    echo "<p>موضع العملة: {$currency_position}</p>";
    
    foreach ($test_amounts as $amount) {
        switch ($currency_position) {
            case 'left':
                $formatted = $currency_symbol . number_format($amount, 2);
                break;
            case 'right':
                $formatted = number_format($amount, 2) . ' ' . $currency_symbol;
                break;
            case 'left_space':
                $formatted = $currency_symbol . ' ' . number_format($amount, 2);
                break;
            case 'right_space':
            default:
                $formatted = number_format($amount, 2) . ' ' . $currency_symbol;
                break;
        }
        echo "<p>المبلغ {$amount} يصبح: {$formatted}</p>";
    }
}

// دالة اختبار القالب
function test_message_template() {
    echo "<h3>اختبار قالب الرسالة:</h3>";
    
    $template = get_default_message_template();
    echo "<h4>القالب الافتراضي:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 4px; direction: rtl;'>" . esc_html($template) . "</pre>";
    
    // بيانات وهمية للاختبار
    $sample_data = [
        '{order_number}' => '12345',
        '{customer_name}' => 'أحمد محمد علي',
        '{customer_phone}' => '+213555123456',
        '{customer_email}' => '<EMAIL>',
        '{shipping_country}' => 'الجزائر',
        '{shipping_state}' => 'الجزائر العاصمة',
        '{shipping_city}' => 'سيدي امحمد',
        '{shipping_address}' => 'شارع الاستقلال، حي البدر، رقم 123',
        '{shipping_method}' => 'توصيل سريع (24 ساعة)',
        '{payment_method}' => 'الدفع عند الاستلام',
        '{order_total}' => '2,500.00 دج',
        '{order_items}' => "📦 هاتف ذكي x 1\n📦 غطاء حماية x 1\n📦 شاحن سريع x 1",
        '{order_status}' => 'قيد المعالجة',
        '{order_date}' => date_i18n('Y-m-d H:i:s'),
        '{order_notes}' => 'يرجى التوصيل بعد الساعة 2 ظهراً، والاتصال قبل الوصول'
    ];
    
    $preview = str_replace(array_keys($sample_data), array_values($sample_data), $template);
    
    echo "<h4>معاينة الرسالة مع البيانات الوهمية:</h4>";
    echo "<div style='background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; white-space: pre-wrap; direction: rtl;'>" . esc_html($preview) . "</div>";
}

// دالة اختبار المتغيرات المتاحة
function test_available_variables() {
    echo "<h3>المتغيرات المتاحة:</h3>";
    
    $variables = [
        '{order_number}' => 'رقم الطلب',
        '{customer_name}' => 'اسم العميل',
        '{customer_phone}' => 'رقم الهاتف',
        '{customer_email}' => 'البريد الإلكتروني',
        '{shipping_country}' => 'الدولة',
        '{shipping_state}' => 'الولاية',
        '{shipping_city}' => 'البلدية',
        '{shipping_address}' => 'العنوان',
        '{shipping_method}' => 'طريقة التوصيل',
        '{payment_method}' => 'طريقة الدفع',
        '{order_total}' => 'المجموع الإجمالي',
        '{order_items}' => 'قائمة المنتجات',
        '{order_status}' => 'حالة الطلب',
        '{order_date}' => 'تاريخ الطلب',
        '{order_notes}' => 'ملاحظات الطلب'
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
    echo "<tr style='background: #f9f9f9;'><th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>المتغير</th><th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>الوصف</th></tr>";
    
    foreach ($variables as $variable => $description) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 10px; font-family: monospace; background: #f8f9fa;'><code>{$variable}</code></td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$description}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// دالة اختبار إعدادات WooCommerce
function test_woocommerce_settings() {
    echo "<h3>إعدادات WooCommerce:</h3>";
    
    $settings = [
        'العملة' => get_woocommerce_currency(),
        'رمز العملة' => get_woocommerce_currency_symbol(),
        'موضع العملة' => get_option('woocommerce_currency_pos'),
        'فاصل الآلاف' => get_option('woocommerce_price_thousand_sep'),
        'فاصل العشرية' => get_option('woocommerce_price_decimal_sep'),
        'عدد الخانات العشرية' => get_option('woocommerce_price_num_decimals')
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
    echo "<tr style='background: #f9f9f9;'><th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>الإعداد</th><th style='border: 1px solid #ddd; padding: 10px; text-align: right;'>القيمة</th></tr>";
    
    foreach ($settings as $setting => $value) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$setting}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px; font-family: monospace;'>{$value}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// عرض صفحة الاختبار
function display_test_page() {
    if (!current_user_can('manage_options')) {
        wp_die('غير مصرح لك بالوصول لهذه الصفحة');
    }
    
    echo "<div class='wrap'>";
    echo "<h1>🧪 اختبار تخصيص رسائل Telegram</h1>";
    echo "<div style='background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);'>";
    
    test_woocommerce_settings();
    test_price_formatting();
    test_available_variables();
    test_message_template();
    
    echo "</div>";
    echo "</div>";
}

// إضافة صفحة الاختبار لقائمة الإدارة (للتطوير فقط)
if (defined('WP_DEBUG') && WP_DEBUG) {
    add_action('admin_menu', function() {
        add_submenu_page(
            'options-general.php',
            'اختبار Telegram',
            'اختبار Telegram',
            'manage_options',
            'test-telegram-messages',
            'display_test_page'
        );
    });
}
